import pygame
import math
import sys
import os
import json
import time
import random
import threading
from datetime import datetime, timedelta
from settings_manager import SettingsManager
from screen_mode_manager import get_screen_mode_manager
from player_storage import load_players_from_json
from common_header import draw_wow_bingo_header

# Import simple stats provider
try:
    from simple_stats_provider import get_simple_stats_provider
    SIMPLE_STATS_AVAILABLE = True
except ImportError:
    SIMPLE_STATS_AVAILABLE = False

# Import performance-optimized stats provider
try:
    from performance_optimized_stats_provider import get_performance_optimized_stats_provider
    PERFORMANCE_OPTIMIZED_AVAILABLE = True
    print("Performance-optimized stats provider available")
except ImportError:
    PERFORMANCE_OPTIMIZED_AVAILABLE = False
    print("Performance-optimized stats provider not available")

# Import emergency stats provider
try:
    from emergency_stats_provider import get_emergency_stats_provider
    EMERGENCY_STATS_AVAILABLE = True
    print("Emergency stats provider available")
except ImportError:
    EMERGENCY_STATS_AVAILABLE = False
    print("Emergency stats provider not available")
    print("Performance-optimized stats provider not available")

# Import stats database
try:
    from stats_db import get_stats_db_manager
    from stats_integration import get_stats_summary, get_game_history, migrate_legacy_stats, force_refresh_data
    STATS_DB_AVAILABLE = True
    print("Stats database modules imported successfully")
except ImportError as e:
    STATS_DB_AVAILABLE = False
    print(f"Stats database not available: {e}")
    print("Advanced statistics will be disabled.")

# Import hybrid database integration
try:
    from hybrid_db_integration import get_hybrid_db_integration
    HYBRID_DB_AVAILABLE = True
    print("Hybrid database integration loaded")
except ImportError as e:
    HYBRID_DB_AVAILABLE = False
    print(f"Hybrid database not available: {e}")
    print("Using SQLite only")

# Try to import game state handler for real-time data
try:
    from game_state_handler import get_current_game_state
    from game_stats_integration import integrate_with_game_state
    GAME_STATE_AVAILABLE = True
    print("Game state handler imported successfully")
except ImportError as e:
    GAME_STATE_AVAILABLE = False
    print(f"Game state handler not available: {e}")
    print("Real-time game statistics will be disabled.")

# Try to import performance monitoring
try:
    from monitor_stats_performance import get_performance_monitor, time_operation
    PERFORMANCE_MONITORING_AVAILABLE = True
    performance_monitor = get_performance_monitor()
    print("Performance monitoring enabled for stats page")
except ImportError as e:
    PERFORMANCE_MONITORING_AVAILABLE = False
    print(f"Performance monitoring not available: {e}")

    # Create dummy decorator if performance monitoring is not available
    def time_operation(operation_name=None):
        def decorator(func):
            return func
        return decorator

# Import payment system
try:
    print("Attempting to import payment system modules...")
    from payment import get_voucher_manager
    print("Successfully imported get_voucher_manager")
    from payment.simple_integration import integrate_with_stats_page
    print("Successfully imported simple_integration")
    PAYMENT_SYSTEM_AVAILABLE = True
    print("Payment system modules imported successfully")
except ImportError as e:
    PAYMENT_SYSTEM_AVAILABLE = False
    print(f"Payment system not available: {e}")
    print("Recharge functionality will be disabled.")

# Import admin integration
try:
    print("Attempting to import admin integration module...")
    from admin_integration import add_admin_button_to_stats_page
    print("Successfully imported admin_integration")
    ADMIN_INTEGRATION_AVAILABLE = True
    print("Admin integration module imported successfully")
except ImportError as e:
    ADMIN_INTEGRATION_AVAILABLE = False
    print(f"Admin integration not available: {e}")
    print("Admin functionality will be disabled.")

# Define colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (128, 128, 128)
LIGHT_GRAY = (200, 200, 200)
DARK_GRAY = (50, 50, 50)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
LIGHT_BLUE = (100, 150, 255)
GOLD = (255, 215, 0)
ORANGE = (255, 165, 0)
NAV_BAR_BG = (30, 40, 50)

# Colors for the new stats page
CARD_BG = (30, 30, 30)
CARD_HEADER_BG = (40, 40, 40)
TABLE_HEADER_BG = (25, 35, 60)
TABLE_ROW_BG_1 = (40, 50, 80)
TABLE_ROW_BG_2 = (50, 60, 90)
ETB_GREEN = (0, 180, 0)
TOTAL_CARD_BG = (20, 20, 20)
SUNDAY_COLOR = (220, 100, 100)
MONDAY_COLOR = (100, 180, 220)
TUESDAY_COLOR = (180, 120, 220)
WEDNESDAY_COLOR = (100, 200, 150)
THURSDAY_COLOR = (220, 180, 100)
FRIDAY_COLOR = (100, 150, 220)
SATURDAY_COLOR = (220, 120, 150)

class CentralizedStatsProvider:
    """
    Centralized stats data provider that ensures consistent data across all sections.
    This class implements unified filtering logic and date calculations.
    """

    def __init__(self):
        import time
        self._cache = {}
        self._cache_timeout = 300  # CRITICAL FIX: 5 minutes cache timeout for better performance
        self._last_cache_time = {}
        self._time = time  # Store time module reference
        self._loading_in_progress = set()  # Track ongoing operations to prevent duplicates

    def get_daily_earnings(self, date_str):
        """
        PERFORMANCE OPTIMIZED: Get daily earnings with aggressive caching.

        Args:
            date_str: Date string in format 'YYYY-MM-DD'

        Returns:
            float: Daily earnings for the specified date
        """
        cache_key = f"daily_earnings_{date_str}"

        # Check cache first - ALWAYS use cache if available
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # PERFORMANCE: Return 0 immediately if no database available
        if not STATS_DB_AVAILABLE:
            self._cache[cache_key] = 0.0
            self._last_cache_time[cache_key] = self._time.time()
            return 0.0

        try:
            # PERFORMANCE: Use timeout to prevent UI blocking
            import queue

            result_queue = queue.Queue()

            def get_earnings_async():
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()
                    earnings = stats_db.get_daily_earnings(date_str)
                    result_queue.put(('success', earnings))
                except Exception as e:
                    result_queue.put(('error', str(e)))

            # Start async operation
            thread = threading.Thread(target=get_earnings_async, daemon=True)
            thread.start()

            # Wait with timeout to prevent UI blocking
            try:
                result_type, result_value = result_queue.get(timeout=0.5)  # 500ms timeout
                if result_type == 'success':
                    earnings = result_value
                else:
                    print(f"PERFORMANCE: Database error (using cache): {result_value}")
                    earnings = 0.0
            except queue.Empty:
                print(f"PERFORMANCE: Database timeout for {date_str} (using cache)")
                earnings = 0.0

            # Cache the result with longer timeout for performance
            self._cache[cache_key] = earnings
            self._last_cache_time[cache_key] = self._time.time()

            return earnings

        except Exception as e:
            print(f"PERFORMANCE: Error getting daily earnings for {date_str}: {e}")
            # Cache zero result to prevent repeated failures
            self._cache[cache_key] = 0.0
            self._last_cache_time[cache_key] = self._time.time()
            return 0.0

    def get_weekly_stats(self, end_date=None):
        """
        PERFORMANCE OPTIMIZED: Get weekly stats with aggressive caching and fallback.

        Args:
            end_date: End date, defaults to today

        Returns:
            list: List of daily stats for the week
        """
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')

        cache_key = f"weekly_stats_{end_date.strftime('%Y-%m-%d')}"

        # PERFORMANCE: Always use cache if available
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # PERFORMANCE: Prevent duplicate loading
        if cache_key in self._loading_in_progress:
            print(f"PERFORMANCE: Weekly stats already loading for {end_date.strftime('%Y-%m-%d')}, using fallback")
            return self._generate_fallback_weekly_stats(end_date)

        try:
            self._loading_in_progress.add(cache_key)

            # PERFORMANCE: Generate minimal stats quickly
            start_date = end_date - timedelta(days=6)  # 7 days total
            weekly_stats = []
            current_date = start_date

            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')

                # PERFORMANCE: Use cached values or defaults
                earnings = 0.0
                games_played = 0

                # Try to get from cache first
                earnings_cache_key = f"daily_earnings_{date_str}"
                games_cache_key = f"daily_games_{date_str}"

                if self._is_cache_valid(earnings_cache_key):
                    earnings = self._cache[earnings_cache_key]

                if self._is_cache_valid(games_cache_key):
                    games_played = self._cache[games_cache_key]

                day_stats = {
                    'date': date_str,
                    'games_played': games_played,
                    'earnings': earnings,
                    'winners': 0,
                    'total_players': 0
                }

                weekly_stats.append(day_stats)
                current_date += timedelta(days=1)

            # Cache the result with longer timeout
            self._cache[cache_key] = weekly_stats
            self._last_cache_time[cache_key] = self._time.time()

            return weekly_stats

        except Exception as e:
            print(f"PERFORMANCE: Error in weekly stats calculation: {e}")
            return self._generate_fallback_weekly_stats(end_date)
        finally:
            self._loading_in_progress.discard(cache_key)

    def _generate_fallback_weekly_stats(self, end_date):
        """Generate fallback weekly stats with zero values for performance."""
        start_date = end_date - timedelta(days=6)
        weekly_stats = []
        current_date = start_date

        while current_date <= end_date:
            day_stats = {
                'date': current_date.strftime('%Y-%m-%d'),
                'games_played': 0,
                'earnings': 0.0,
                'winners': 0,
                'total_players': 0
            }
            weekly_stats.append(day_stats)
            current_date += timedelta(days=1)

        return weekly_stats

    def _get_daily_games_played(self, date_str):
        """PERFORMANCE OPTIMIZED: Get daily games played with caching."""
        cache_key = f"daily_games_{date_str}"

        # PERFORMANCE: Always use cache if available
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # PERFORMANCE: Return 0 immediately if no database available
        if not STATS_DB_AVAILABLE:
            self._cache[cache_key] = 0
            self._last_cache_time[cache_key] = self._time.time()
            return 0

        try:
            # PERFORMANCE: Use timeout to prevent UI blocking
            import queue

            result_queue = queue.Queue()

            def get_games_async():
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()
                    games = stats_db.get_daily_games_played(date_str)
                    result_queue.put(('success', games))
                except Exception as e:
                    result_queue.put(('error', str(e)))

            # Start async operation
            thread = threading.Thread(target=get_games_async, daemon=True)
            thread.start()

            # Wait with timeout to prevent UI blocking
            try:
                result_type, result_value = result_queue.get(timeout=0.5)  # 500ms timeout
                if result_type == 'success':
                    games = result_value
                else:
                    print(f"PERFORMANCE: Database error for games {date_str}: {result_value}")
                    games = 0
            except queue.Empty:
                print(f"PERFORMANCE: Database timeout for games {date_str}")
                games = 0

            # Cache the result
            self._cache[cache_key] = games
            self._last_cache_time[cache_key] = self._time.time()

            return games

        except Exception as e:
            print(f"PERFORMANCE: Error getting daily games for {date_str}: {e}")
            # Cache zero result to prevent repeated failures
            self._cache[cache_key] = 0
            self._last_cache_time[cache_key] = self._time.time()
            return 0

    def _is_cache_valid(self, cache_key):
        """Check if cached data is still valid."""
        if cache_key not in self._cache:
            return False
        if cache_key not in self._last_cache_time:
            return False
        return (self._time.time() - self._last_cache_time[cache_key]) < self._cache_timeout

    def get_monthly_stats(self, end_date=None):
        """
        PERFORMANCE OPTIMIZED: Get monthly stats with aggressive caching and fallback.

        Args:
            end_date: End date, defaults to today

        Returns:
            list: List of monthly stats for the last 6 months
        """
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')

        cache_key = f"monthly_stats_{end_date.strftime('%Y-%m')}"

        # PERFORMANCE: Always use cache if available
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # PERFORMANCE: Prevent duplicate loading
        if cache_key in self._loading_in_progress:
            print(f"PERFORMANCE: Monthly stats already loading for {end_date.strftime('%Y-%m')}, using fallback")
            return self._generate_fallback_monthly_stats(end_date)

        try:
            self._loading_in_progress.add(cache_key)

            # Get last 6 months of data
            monthly_stats = []
            
            # Start from 5 months ago (to get 6 months total including current)
            for i in range(5, -1, -1):
                # Calculate month date
                if i > 0:
                    # For previous months, get the first day of the month
                    month_date = end_date.replace(day=1)
                    for _ in range(i):
                        # Go back to previous month
                        month_date = month_date.replace(day=1) - timedelta(days=1)
                        month_date = month_date.replace(day=1)  # First day of that month
                else:
                    # For current month, use current date
                    month_date = end_date
                
                month_name = month_date.strftime('%b %Y')
                month_key = month_date.strftime('%Y-%m')
                
                # Get real earnings data for this month
                earnings = self._get_monthly_earnings(month_date.year, month_date.month)
                games_played = self._get_monthly_games_played(month_date.year, month_date.month)
                
                monthly_stats.append({
                    'name': month_name,
                    'date': month_key,
                    'earnings': earnings,
                    'games_played': games_played,
                    'winners': 0,  # Not tracking monthly winners for now
                    'total_players': 0  # Not tracking monthly players for now
                })

            # Cache the result with longer timeout
            self._cache[cache_key] = monthly_stats
            self._last_cache_time[cache_key] = self._time.time()

            return monthly_stats

        except Exception as e:
            print(f"PERFORMANCE: Error in monthly stats calculation: {e}")
            return self._generate_fallback_monthly_stats(end_date)
        finally:
            self._loading_in_progress.discard(cache_key)

    def _generate_fallback_monthly_stats(self, end_date):
        """Generate fallback monthly stats with zero values for performance."""
        monthly_stats = []
        
        # Generate 6 months of fallback data
        for i in range(5, -1, -1):
            # Calculate month date
            if i > 0:
                month_date = end_date.replace(day=1)
                for _ in range(i):
                    month_date = month_date.replace(day=1) - timedelta(days=1)
                    month_date = month_date.replace(day=1)
            else:
                month_date = end_date
            
            month_name = month_date.strftime('%b %Y')
            month_key = month_date.strftime('%Y-%m')
            
            monthly_stats.append({
                'name': month_name,
                'date': month_key,
                'earnings': 0.0,
                'games_played': 0,
                'winners': 0,
                'total_players': 0
            })

        return monthly_stats

    def get_yearly_stats(self, end_date=None):
        """
        PERFORMANCE OPTIMIZED: Get yearly stats with aggressive caching and fallback.

        Args:
            end_date: End date, defaults to today

        Returns:
            list: List of yearly stats for the last 3 years
        """
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')

        cache_key = f"yearly_stats_{end_date.strftime('%Y')}"

        # PERFORMANCE: Always use cache if available
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # PERFORMANCE: Prevent duplicate loading
        if cache_key in self._loading_in_progress:
            print(f"PERFORMANCE: Yearly stats already loading for {end_date.strftime('%Y')}, using fallback")
            return self._generate_fallback_yearly_stats(end_date)

        try:
            self._loading_in_progress.add(cache_key)

            # Get last 3 years of data
            yearly_stats = []
            current_year = end_date.year
            
            for i in range(2, -1, -1):
                year = current_year - i
                year_name = str(year)
                
                # Get real earnings data for this year
                earnings = self._get_yearly_earnings(year)
                games_played = self._get_yearly_games_played(year)
                
                yearly_stats.append({
                    'name': year_name,
                    'date': year_name,
                    'earnings': earnings,
                    'games_played': games_played,
                    'winners': 0,  # Not tracking yearly winners for now
                    'total_players': 0  # Not tracking yearly players for now
                })

            # Cache the result with longer timeout
            self._cache[cache_key] = yearly_stats
            self._last_cache_time[cache_key] = self._time.time()

            return yearly_stats

        except Exception as e:
            print(f"PERFORMANCE: Error in yearly stats calculation: {e}")
            return self._generate_fallback_yearly_stats(end_date)
        finally:
            self._loading_in_progress.discard(cache_key)

    def _generate_fallback_yearly_stats(self, end_date):
        """Generate fallback yearly stats with zero values for performance."""
        yearly_stats = []
        current_year = end_date.year
        
        for i in range(2, -1, -1):
            year = current_year - i
            year_name = str(year)
            
            yearly_stats.append({
                'name': year_name,
                'date': year_name,
                'earnings': 0.0,
                'games_played': 0,
                'winners': 0,
                'total_players': 0
            })

        return yearly_stats

    def _get_monthly_earnings(self, year, month):
        """
        PERFORMANCE OPTIMIZED: Get monthly earnings with caching.

        Args:
            year: Year (e.g., 2023)
            month: Month (1-12)

        Returns:
            float: Monthly earnings for the specified month
        """
        cache_key = f"monthly_earnings_{year}-{month:02d}"

        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # Return 0 immediately if no database available
        if not STATS_DB_AVAILABLE:
            self._cache[cache_key] = 0.0
            self._last_cache_time[cache_key] = self._time.time()
            return 0.0

        try:
            # Use timeout to prevent UI blocking
            import queue

            result_queue = queue.Queue()

            def get_earnings_async():
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()
                    
                    # Calculate start and end dates for the month
                    import calendar
                    start_date = f"{year}-{month:02d}-01"
                    _, last_day = calendar.monthrange(year, month)
                    end_date = f"{year}-{month:02d}-{last_day:02d}"
                    
                    # Get daily stats for each day in the month
                    with stats_db.get_connection_context() as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                        SELECT SUM(earnings) FROM daily_stats
                        WHERE date BETWEEN ? AND ?
                        ''', (start_date, end_date))
                        
                        result = cursor.fetchone()
                        earnings = result[0] if result and result[0] is not None else 0.0
                        
                        # Also check game_history for fees
                        cursor.execute('''
                        SELECT SUM(fee) FROM game_history
                        WHERE date(date_time) BETWEEN ? AND ?
                        AND username NOT LIKE '%Game Reset%'
                        AND username NOT LIKE '%Demo%'
                        AND total_calls > 0
                        AND status NOT LIKE '%cancelled%'
                        AND status NOT LIKE '%aborted%'
                        ''', (start_date, end_date))
                        
                        result = cursor.fetchone()
                        fees = result[0] if result and result[0] is not None else 0.0
                        
                        # Use the larger of the two values
                        earnings = max(earnings, fees)
                    
                    result_queue.put(('success', earnings))
                except Exception as e:
                    result_queue.put(('error', str(e)))

            # Start async operation
            thread = threading.Thread(target=get_earnings_async, daemon=True)
            thread.start()

            # Wait with timeout to prevent UI blocking
            try:
                result_type, result_value = result_queue.get(timeout=0.5)  # 500ms timeout
                if result_type == 'success':
                    earnings = result_value
                else:
                    print(f"PERFORMANCE: Database error (using cache): {result_value}")
                    earnings = 0.0
            except queue.Empty:
                print(f"PERFORMANCE: Database timeout for {year}-{month:02d} (using cache)")
                earnings = 0.0

            # Cache the result with longer timeout for performance
            self._cache[cache_key] = earnings
            self._last_cache_time[cache_key] = self._time.time()

            return earnings

        except Exception as e:
            print(f"PERFORMANCE: Error getting monthly earnings for {year}-{month:02d}: {e}")
            # Cache zero result to prevent repeated failures
            self._cache[cache_key] = 0.0
            self._last_cache_time[cache_key] = self._time.time()
            return 0.0

    def _get_monthly_games_played(self, year, month):
        """
        PERFORMANCE OPTIMIZED: Get monthly games played with caching.

        Args:
            year: Year (e.g., 2023)
            month: Month (1-12)

        Returns:
            int: Monthly games played for the specified month
        """
        cache_key = f"monthly_games_{year}-{month:02d}"

        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # Return 0 immediately if no database available
        if not STATS_DB_AVAILABLE:
            self._cache[cache_key] = 0
            self._last_cache_time[cache_key] = self._time.time()
            return 0

        try:
            # Use timeout to prevent UI blocking
            import queue

            result_queue = queue.Queue()

            def get_games_async():
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()
                    
                    # Calculate start and end dates for the month
                    import calendar
                    start_date = f"{year}-{month:02d}-01"
                    _, last_day = calendar.monthrange(year, month)
                    end_date = f"{year}-{month:02d}-{last_day:02d}"
                    
                    # Get daily stats for each day in the month
                    with stats_db.get_connection_context() as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                        SELECT SUM(games_played) FROM daily_stats
                        WHERE date BETWEEN ? AND ?
                        ''', (start_date, end_date))
                        
                        result = cursor.fetchone()
                        games = result[0] if result and result[0] is not None else 0
                        
                        # Also check game_history
                        cursor.execute('''
                        SELECT COUNT(*) FROM game_history
                        WHERE date(date_time) BETWEEN ? AND ?
                        AND username NOT LIKE '%Game Reset%'
                        AND username NOT LIKE '%Demo%'
                        AND total_calls > 0
                        AND status NOT LIKE '%cancelled%'
                        AND status NOT LIKE '%aborted%'
                        ''', (start_date, end_date))
                        
                        result = cursor.fetchone()
                        history_games = result[0] if result and result[0] is not None else 0
                        
                        # Use the larger of the two values
                        games = max(games, history_games)
                    
                    result_queue.put(('success', games))
                except Exception as e:
                    result_queue.put(('error', str(e)))

            # Start async operation
            thread = threading.Thread(target=get_games_async, daemon=True)
            thread.start()

            # Wait with timeout to prevent UI blocking
            try:
                result_type, result_value = result_queue.get(timeout=0.5)  # 500ms timeout
                if result_type == 'success':
                    games = result_value
                else:
                    print(f"PERFORMANCE: Database error for games {year}-{month:02d}: {result_value}")
                    games = 0
            except queue.Empty:
                print(f"PERFORMANCE: Database timeout for games {year}-{month:02d}")
                games = 0

            # Cache the result
            self._cache[cache_key] = games
            self._last_cache_time[cache_key] = self._time.time()

            return games

        except Exception as e:
            print(f"PERFORMANCE: Error getting monthly games for {year}-{month:02d}: {e}")
            # Cache zero result to prevent repeated failures
            self._cache[cache_key] = 0
            self._last_cache_time[cache_key] = self._time.time()
            return 0

    def _get_yearly_earnings(self, year):
        """
        PERFORMANCE OPTIMIZED: Get yearly earnings with caching.

        Args:
            year: Year (e.g., 2023)

        Returns:
            float: Yearly earnings for the specified year
        """
        cache_key = f"yearly_earnings_{year}"

        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # Return 0 immediately if no database available
        if not STATS_DB_AVAILABLE:
            self._cache[cache_key] = 0.0
            self._last_cache_time[cache_key] = self._time.time()
            return 0.0

        try:
            # Use timeout to prevent UI blocking
            import queue

            result_queue = queue.Queue()

            def get_earnings_async():
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()
                    
                    # Calculate start and end dates for the year
                    start_date = f"{year}-01-01"
                    end_date = f"{year}-12-31"
                    
                    # Get daily stats for each day in the year
                    with stats_db.get_connection_context() as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                        SELECT SUM(earnings) FROM daily_stats
                        WHERE date BETWEEN ? AND ?
                        ''', (start_date, end_date))
                        
                        result = cursor.fetchone()
                        earnings = result[0] if result and result[0] is not None else 0.0
                        
                        # Also check game_history for fees
                        cursor.execute('''
                        SELECT SUM(fee) FROM game_history
                        WHERE date(date_time) BETWEEN ? AND ?
                        AND username NOT LIKE '%Game Reset%'
                        AND username NOT LIKE '%Demo%'
                        AND total_calls > 0
                        AND status NOT LIKE '%cancelled%'
                        AND status NOT LIKE '%aborted%'
                        ''', (start_date, end_date))
                        
                        result = cursor.fetchone()
                        fees = result[0] if result and result[0] is not None else 0.0
                        
                        # Use the larger of the two values
                        earnings = max(earnings, fees)
                    
                    result_queue.put(('success', earnings))
                except Exception as e:
                    result_queue.put(('error', str(e)))

            # Start async operation
            thread = threading.Thread(target=get_earnings_async, daemon=True)
            thread.start()

            # Wait with timeout to prevent UI blocking
            try:
                result_type, result_value = result_queue.get(timeout=0.5)  # 500ms timeout
                if result_type == 'success':
                    earnings = result_value
                else:
                    print(f"PERFORMANCE: Database error (using cache): {result_value}")
                    earnings = 0.0
            except queue.Empty:
                print(f"PERFORMANCE: Database timeout for {year} (using cache)")
                earnings = 0.0

            # Cache the result with longer timeout for performance
            self._cache[cache_key] = earnings
            self._last_cache_time[cache_key] = self._time.time()

            return earnings

        except Exception as e:
            print(f"PERFORMANCE: Error getting yearly earnings for {year}: {e}")
            # Cache zero result to prevent repeated failures
            self._cache[cache_key] = 0.0
            self._last_cache_time[cache_key] = self._time.time()
            return 0.0

    def _get_yearly_games_played(self, year):
        """
        PERFORMANCE OPTIMIZED: Get yearly games played with caching.

        Args:
            year: Year (e.g., 2023)

        Returns:
            int: Yearly games played for the specified year
        """
        cache_key = f"yearly_games_{year}"

        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # Return 0 immediately if no database available
        if not STATS_DB_AVAILABLE:
            self._cache[cache_key] = 0
            self._last_cache_time[cache_key] = self._time.time()
            return 0

        try:
            # Use timeout to prevent UI blocking
            import queue

            result_queue = queue.Queue()

            def get_games_async():
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()
                    
                    # Calculate start and end dates for the year
                    start_date = f"{year}-01-01"
                    end_date = f"{year}-12-31"
                    
                    # Get daily stats for each day in the year
                    with stats_db.get_connection_context() as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                        SELECT SUM(games_played) FROM daily_stats
                        WHERE date BETWEEN ? AND ?
                        ''', (start_date, end_date))
                        
                        result = cursor.fetchone()
                        games = result[0] if result and result[0] is not None else 0
                        
                        # Also check game_history
                        cursor.execute('''
                        SELECT COUNT(*) FROM game_history
                        WHERE date(date_time) BETWEEN ? AND ?
                        AND username NOT LIKE '%Game Reset%'
                        AND username NOT LIKE '%Demo%'
                        AND total_calls > 0
                        AND status NOT LIKE '%cancelled%'
                        AND status NOT LIKE '%aborted%'
                        ''', (start_date, end_date))
                        
                        result = cursor.fetchone()
                        history_games = result[0] if result and result[0] is not None else 0
                        
                        # Use the larger of the two values
                        games = max(games, history_games)
                    
                    result_queue.put(('success', games))
                except Exception as e:
                    result_queue.put(('error', str(e)))

            # Start async operation
            thread = threading.Thread(target=get_games_async, daemon=True)
            thread.start()

            # Wait with timeout to prevent UI blocking
            try:
                result_type, result_value = result_queue.get(timeout=0.5)  # 500ms timeout
                if result_type == 'success':
                    games = result_value
                else:
                    print(f"PERFORMANCE: Database error for games {year}: {result_value}")
                    games = 0
            except queue.Empty:
                print(f"PERFORMANCE: Database timeout for games {year}")
                games = 0

            # Cache the result
            self._cache[cache_key] = games
            self._last_cache_time[cache_key] = self._time.time()

            return games

        except Exception as e:
            print(f"PERFORMANCE: Error getting yearly games for {year}: {e}")
            # Cache zero result to prevent repeated failures
            self._cache[cache_key] = 0
            self._last_cache_time[cache_key] = self._time.time()
            return 0

    def clear_cache(self):
        """Clear all cached data."""
        self._cache.clear()
        self._last_cache_time.clear()
        print("CENTRALIZED: Cache cleared")

def show_stats_page(screen, on_close_callback=None, previous_page=None):
    """
    CRITICAL FIX: Show the stats page with instant loading and no blocking operations

    Args:
        screen: Pygame screen surface
        on_close_callback: Optional callback function to execute when the stats page is closed
        previous_page: Optional string indicating which page navigated to stats ('board_selection', 'main_game', etc.)

    Returns:
        None
    """
    print("CRITICAL FIX: Starting instant stats page loading...")
    start_time = time.time()
    
    try:
        stats_page = StatsPage(screen, on_close_callback, previous_page)
    except Exception as e:
        print(f"Error creating StatsPage: {e}")
        import traceback
        traceback.print_exc()
        return

    # CRITICAL FIX: Skip all blocking integrations during initialization
    print("CRITICAL FIX: Skipping blocking integrations for instant loading...")
    
    # Apply critical fixes
    try:
        from stats_page_critical_fixes import apply_critical_fixes_to_stats_page
        apply_critical_fixes_to_stats_page(stats_page)
        print("CRITICAL FIX: Performance and anti-blinking fixes applied")
    except ImportError:
        print("CRITICAL FIX: Critical fixes module not found, using basic optimizations")
    
    # Integrate payment system in background (non-blocking)
    if PAYMENT_SYSTEM_AVAILABLE:
        def integrate_payment_background():
            try:
                time.sleep(1)  # Let UI load first
                integrate_with_stats_page(stats_page)
                print("Background payment integration completed")
            except Exception as e:
                print(f"Background payment integration error: {e}")
        
        threading.Thread(target=integrate_payment_background, daemon=True).start()

    # Integrate admin UI in background (non-blocking)
    if ADMIN_INTEGRATION_AVAILABLE:
        def integrate_admin_background():
            try:
                time.sleep(1.5)  # Let UI load first
                add_admin_button_to_stats_page(stats_page)
                print("Background admin integration completed")
            except Exception as e:
                print(f"Background admin integration error: {e}")
        
        threading.Thread(target=integrate_admin_background, daemon=True).start()

    try:
        stats_page.run()
        end_time = time.time()
        print(f"CRITICAL FIX: Stats page loaded in {end_time - start_time:.2f} seconds")
    except Exception as e:
        print(f"Error running stats page: {e}")
        import traceback
        traceback.print_exc()

class StatsPage:
    def __init__(self, screen, on_close_callback=None, previous_page=None):
        self.screen = screen
        self.running = True
        self.scale_x = screen.get_width() / 1024
        self.scale_y = screen.get_height() / 768
        self.on_close_callback = on_close_callback
        self.integrated_mode = on_close_callback is not None
        self.previous_page = previous_page or "unknown"  # Track where we came from

        # Load settings
        try:
            self.settings_manager = SettingsManager()
            self.settings = self.settings_manager.load_settings()
            if not isinstance(self.settings, dict):
                self.settings = {}
        except Exception as e:
            print(f"Error loading settings: {str(e)}")
            self.settings = {}

        # Initialize screen mode manager
        self.screen_mode_manager = get_screen_mode_manager()
        self.screen_mode_manager.set_screen_reference(screen)

        # Initialize UI elements
        self.hit_areas = {}
        self.button_states = {}
        self.init_button_animations()

        # Message display
        self.message = ""
        self.message_timer = 0
        self.message_type = "info"

        # Active navigation section
        self.active_nav = "stats"

        # Initialize font cache to improve performance
        self._font_cache = {}

        # Initialize surface caches
        self._gradient_cache = {}
        self._section_cache = {}

        # Game history pagination - FIXED: Large page size to show ALL history without cutting off
        self.history_page = 0
        self.history_page_size = 1000  # Large page size to ensure all history is shown
        self.total_history_pages = 1
        
        # Store all game history records for accurate remainder calculation
        self.all_game_history = []
        self.game_history = []

        # Table interaction state
        self.selected_row_index = -1  # Index of currently selected row (-1 means none)
        self.hovered_row_index = -1   # Index of currently hovered row (-1 means none)
        self.table_rows = []          # Store table row rectangles for hit detection
        self.last_click_time = 0      # For double-click detection

        # Credit recharge history state
        self.credit_history_visible = False  # Whether credit history section is visible
        self.credit_history = []             # Credit recharge history data
        self.credit_selected_row_index = -1  # Selected row in credit history table
        self.credit_hovered_row_index = -1   # Hovered row in credit history table
        self.credit_table_rows = []          # Credit table row rectangles for hit detection
        self.credit_search_query = ""        # Search query for credit history
        self.credit_search_active = False    # Whether credit search is active
        self.credit_search_results = []      # Filtered credit search results
        self.original_credit_history = []    # Store original unfiltered credit data

        # Scrolling state
        self.scroll_y = 0                    # Current scroll position
        self.max_scroll_y = 0                # Maximum scroll position
        self.scroll_speed = 30               # Pixels to scroll per wheel event
        self.content_height = 0              # Total height of all content

        # Authentication state
        self.authenticated = False           # Whether user is authenticated
        self.auth_username = ""              # Current authenticated username
        self.auth_session_start = 0          # Session start time
        self.auth_session_timeout = 3600     # Session timeout in seconds (1 hour)
        self.login_username = ""             # Username input field
        self.login_password = ""             # Password input field
        self.login_username_active = False   # Whether username field is active
        self.login_password_active = False   # Whether password field is active
        self.login_error_message = ""        # Login error message
        self.login_error_timer = 0           # Error message timer

        # Time period selection state
        self.current_time_period = "weekly"  # Current time period: daily, weekly, monthly, yearly
        self.time_period_tabs = ["Daily", "Weekly", "Monthly", "Yearly"]
        self.time_period_data = {}           # Cache for different time period data
        
        # Initialize centralized stats provider
        if PERFORMANCE_OPTIMIZED_AVAILABLE:
            self.stats_provider = get_performance_optimized_stats_provider()
            print("Using performance-optimized stats provider")
        elif SIMPLE_STATS_AVAILABLE:
            self.stats_provider = get_simple_stats_provider()
            print("Using simple stats provider")
        else:
            self.stats_provider = CentralizedStatsProvider()
            print("Using original stats provider")
        
        # Pre-load monthly and yearly stats in the background
        # PERFORMANCE FIX: Delayed stats provider initialization
        # self.initialize_stats_provider()

        # Search functionality state
        self.search_query = ""        # Current search query
        self.search_active = False    # Whether search input is focused
        self.search_results = []      # Filtered search results
        self.search_debounce_timer = 0  # Timer for search debouncing
        self.search_debounce_delay = 300  # Milliseconds to wait before searching
        self.last_search_time = 0     # Last time search was performed
        self.search_input_rect = None # Rectangle for search input field
        self.search_clear_rect = None # Rectangle for clear search button
        self.original_game_history = []  # Store original unfiltered data

        # Notification system state
        self.notifications = []       # List of active notifications
        self.notification_height = 0  # Height of notification area
        self.last_notification_check = 0  # Last time notifications were checked

        # Export system state
        self.export_in_progress = False  # Whether export is currently in progress
        self.export_format = "pdf"       # Current export format (pdf, html)
        self.export_progress = 0         # Export progress (0-100)
        self.export_status = ""          # Current export status message

        # Weekly stats display
        self.weekly_stats = []
        self.day_names = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
        self.day_colors = [SUNDAY_COLOR, MONDAY_COLOR, TUESDAY_COLOR, WEDNESDAY_COLOR, THURSDAY_COLOR, FRIDAY_COLOR, SATURDAY_COLOR]

        # Refresh control to prevent duplicate refreshes
        self._last_refresh_time = 0
        self._refresh_cooldown = 2.0  # 2 seconds between refreshes
        self._refresh_in_progress = False
        self._pending_refresh_events = set()  # Track pending refresh event sources

        # PERFORMANCE: Initialize with default values for immediate UI responsiveness
        self.daily_earnings = 0.0
        self.daily_games = 0
        self.total_earnings = 0.0
        self.wallet_balance = 0.0
        self.weekly_stats = []
        self.credit_history = []
        self.original_credit_history = []
        self.notifications = []
        self.notification_height = 0

        # Game history state tracking
        self.game_history = []
        self.original_game_history = []
        self.total_history_pages = 1
        self.game_history_loading_complete = False  # Track if loading is complete
        self.database_has_games = None  # Track if database has any games (None = unknown, True/False = known)
        self._loading_state_lock = threading.Lock()  # Prevent race conditions in loading state
        self._stable_loading_complete = False  # Stable flag that doesn't get reset

        # Load sound effects if enabled (fast operation)
        self.load_sound_effects()

        # PERFORMANCE: Start background loading without blocking initialization
        print("PERFORMANCE: Starting background data loading...")
        loading_thread = threading.Thread(target=self._load_data_background_optimized, daemon=True)
        loading_thread.start()

        print("PERFORMANCE: Stats page initialization completed quickly")
        # FLICKERING FIX: Anti-blinking state management
        self._last_section_update = {}
        self._section_update_cooldown = 5.0  # 5 seconds minimum between section updates (anti-blink)
        self._stable_ui_state = {}
        self._refresh_debounce = {}
        # FINAL FIX: Frame-level caching to prevent blinking
        self._display_state_cache = {}
        self._last_display_evaluation = 0
        self._display_cache_timeout = 1.0  # Cache display decisions for 1 second
        self._last_render_time = {}  # Track last render time for each section
        self._render_cooldown = 1.0  # Minimum 1 second between renders

        # FLICKERING FIX: Initialize loading state properly
        self.stats_loading_in_progress = False
        self.initial_loading_complete = True

        # FLICKERING FIX: Prevent multiple simultaneous loading operations
        self._loading_lock = threading.Lock()
        

    
    def _load_data_background_optimized(self):
        """CRITICAL FIX: Ultra-fast background loading with no blocking operations"""
        try:
            # PERFORMANCE: Minimal delay for UI responsiveness
            time.sleep(0.1)
            
            print("CRITICAL FIX: Starting instant background data loading...")
            
            # Set default values immediately
            self.daily_earnings = 0.0
            self.weekly_stats = []
            self.game_history = []
            self.wallet_balance = 0.0
            
            # CRITICAL FIX: Mark loading as complete and disable loading indicators
            self.initial_loading_complete = True
            self.stats_loading_in_progress = False
            # ANTI-BLINK: Set stable loading state
            if hasattr(self, '_loading_state_lock'):
                with self._loading_state_lock:
                    self._stable_loading_complete = True
                    print("ANTI-BLINK: Background loading completed, stable state set")
            if hasattr(self, 'set_loading_state'):
                self.set_loading_state(False)
            
            print("CRITICAL FIX: Instant background loading completed")
            
        except Exception as e:
            print(f"CRITICAL FIX: Error in instant background loading: {e}")
            self.initial_loading_complete = True
    
    def _load_essential_data(self):
        """Load only essential data first"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            self.daily_earnings = self.stats_provider.get_daily_earnings(today)
        except Exception as e:
            print(f"Error loading essential data: {e}")
            self.daily_earnings = 0.0
    
    def _load_secondary_data(self):
        """Load secondary data"""
        try:
            self.weekly_stats = self.stats_provider.get_weekly_stats()
        except Exception as e:
            print(f"Error loading secondary data: {e}")
            self.weekly_stats = []
    
    def _load_optional_data(self):
        """Load optional data last"""
        try:
            # Load game history in smaller batches
            if hasattr(self.stats_provider, 'get_game_history'):
                self.game_history = self.stats_provider.get_game_history(50)  # Limit to 50 records
            else:
                self.game_history = []
        except Exception as e:
            print(f"Error loading optional data: {e}")
            self.game_history = []

    def _load_data_background(self):
        """PERFORMANCE: Background thread for loading data without blocking UI initialization."""
        try:
            print("BACKGROUND INIT: Starting data loading...")

            # Check if database has any games first
            try:
                print("BACKGROUND INIT: Checking database for game records...")
                from stats_db import get_stats_db_manager
                stats_db = get_stats_db_manager()
                conn = stats_db.get_connection()
                cursor = conn.cursor()

                # Check if game_history table has any records
                cursor.execute('SELECT COUNT(*) FROM game_history')
                game_count = cursor.fetchone()[0]

                self.database_has_games = game_count > 0
                print(f"BACKGROUND INIT: Database has {game_count} game records")

                if game_count > 0:
                    # Load statistics data with timeout protection
                    try:
                        import queue

                        result_queue = queue.Queue()

                        def load_stats_async():
                            try:
                                # Load minimal stats data
                                self.load_statistics()
                                result_queue.put(('stats_success', True))
                            except Exception as e:
                                result_queue.put(('stats_error', str(e)))

                        # Start async loading with timeout
                        thread = threading.Thread(target=load_stats_async, daemon=True)
                        thread.start()

                        try:
                            result_type, result_value = result_queue.get(timeout=2.0)  # 2 second timeout
                            if result_type == 'stats_success':
                                print("BACKGROUND INIT: Statistics loaded successfully")
                            else:
                                print(f"BACKGROUND INIT: Statistics loading error: {result_value}")
                        except queue.Empty:
                            print("BACKGROUND INIT: Statistics loading timeout - using defaults")
                    except Exception as e:
                        print(f"BACKGROUND INIT: Error loading statistics: {e}")
                else:
                    print("BACKGROUND INIT: No games in database - skipping stats loading")

                # Mark game history loading as complete with thread safety
                with self._loading_state_lock:
                    self.game_history_loading_complete = True
                    self._stable_loading_complete = True
                    print("ANTI-BLINK: Game history loading marked as complete")

            except Exception as e:
                print(f"BACKGROUND INIT: Error checking database: {e}")
                self.database_has_games = False
                with self._loading_state_lock:
                    self.game_history_loading_complete = True
                    self._stable_loading_complete = True
                    print("ANTI-BLINK: Loading state stabilized after error")

            # Load credit history with timeout protection
            try:
                self.load_credit_history()
                print("BACKGROUND INIT: Credit history loaded")
            except Exception as e:
                print(f"BACKGROUND INIT: Error loading credit history: {e}")

            # Initialize notifications
            try:
                self.check_notifications()
                print("BACKGROUND INIT: Notifications initialized")
            except Exception as e:
                print(f"BACKGROUND INIT: Error initializing notifications: {e}")

            print("BACKGROUND INIT: Data loading completed")

        except Exception as e:
            print(f"BACKGROUND INIT: Error in background loading: {e}")

    def trigger_notification_update(self):
        """
        Manually trigger a notification update.
        This can be called when credit balance or voucher data changes.
        """
        self.last_notification_check = 0  # Force immediate check
        self.check_notifications()

    def get_current_wallet_balance(self):
        """
        Get the current wallet balance from the voucher manager.

        Returns:
            float: Current wallet balance, or 0 if payment system is not available
        """
        try:
            # First try to get from the recharge UI if available
            if hasattr(self, 'recharge_ui') and hasattr(self.recharge_ui, 'voucher_manager'):
                return float(self.recharge_ui.voucher_manager.credits)

            # Try to get from payment system directly
            if PAYMENT_SYSTEM_AVAILABLE:
                try:
                    from payment import get_voucher_manager
                    voucher_manager = get_voucher_manager()
                    return float(voucher_manager.get_current_credits())
                except Exception as e:
                    print(f"Error getting wallet balance from payment system: {e}")

            # Fallback to stored wallet_balance attribute
            return float(getattr(self, 'wallet_balance', 0))

        except Exception as e:
            print(f"Error retrieving current wallet balance: {e}")
            return 0.0

    def get_machine_uuid(self):
        """
        Get the current machine's UUID for voucher system.

        Returns:
            str: Machine UUID or None if not available
        """
        try:
            # Try to import the get_machine_uuid function
            try:
                from get_machine_uuid import get_machine_uuid
                return get_machine_uuid()
            except ImportError:
                pass

            # Try to get from payment system
            if PAYMENT_SYSTEM_AVAILABLE:
                try:
                    from payment.crypto_utils import CryptoUtils
                    return CryptoUtils.get_machine_uuid()
                except Exception as e:
                    print(f"Error getting UUID from payment system: {e}")

            # Try to get from voucher processor
            try:
                from payment.voucher_processor import VoucherProcessor
                processor = VoucherProcessor()
                return processor.machine_uuid
            except Exception as e:
                print(f"Error getting UUID from voucher processor: {e}")

            # Fallback: try to read from cached file
            try:
                import os
                cached_uuid_path = os.path.join('data', 'machine_uuid.txt')
                if os.path.exists(cached_uuid_path):
                    with open(cached_uuid_path, 'r') as f:
                        return f.read().strip()
            except Exception as e:
                print(f"Error reading cached UUID: {e}")

            return None

        except Exception as e:
            print(f"Error retrieving machine UUID: {e}")
            return None

    def refresh_wallet_balance(self):
        """
        Refresh the wallet balance immediately.
        This method can be called when the wallet balance changes during gameplay.
        """
        try:
            old_balance = getattr(self, 'wallet_balance', 0)
            new_balance = self.get_current_wallet_balance()

            if old_balance != new_balance:
                self.wallet_balance = new_balance
                print(f"Wallet balance updated: {old_balance} -> {new_balance}")

                # Show a message if the balance changed significantly
                if abs(new_balance - old_balance) >= 1.0:
                    if new_balance > old_balance:
                        self.show_message(f"Wallet balance increased to {new_balance:.1f} ETB", "success")
                    else:
                        self.show_message(f"Wallet balance updated to {new_balance:.1f} ETB", "info")

                # Trigger notification update when balance changes
                self.trigger_notification_update()

        except Exception as e:
            print(f"Error refreshing wallet balance: {e}")

    def trigger_wallet_balance_update(self):
        """
        Trigger a wallet balance update event.
        This method can be called from external code when the wallet balance changes.
        """
        try:
            # Create a custom event to trigger wallet balance update
            wallet_event = pygame.event.Event(pygame.USEREVENT, wallet_balance_update=True)
            pygame.event.post(wallet_event)
            print("Wallet balance update event triggered")
        except Exception as e:
            print(f"Error triggering wallet balance update: {e}")

    def load_credit_history(self):
        """
        Load credit recharge history from the payment system.
        """
        try:
            # Check if payment system is available
            if not PAYMENT_SYSTEM_AVAILABLE:
                print("Payment system not available - credit history disabled")
                self.credit_history = []
                self.original_credit_history = []
                return

            # Get voucher manager
            from payment import get_voucher_manager
            voucher_manager = get_voucher_manager()

            # Get comprehensive voucher history (increased limit for better data)
            raw_history = voucher_manager.get_voucher_history(50)  # Get up to 50 records

            # Process and enhance the credit history data
            processed_history = []
            current_balance = voucher_manager.credits  # Current balance

            # Sort by redeemed date (most recent first) and calculate remaining balances
            for i, record in enumerate(raw_history):
                try:
                    # Generate a transaction ID based on timestamp and amount
                    redeemed_at = record.get('redeemed_at', 'Unknown')
                    amount = record.get('amount', 0)

                    # Create a simple transaction ID
                    transaction_id = f"TXN-{hash(f'{redeemed_at}-{amount}') % 100000:05d}"

                    # Calculate remaining balance for this transaction
                    # This is a simplified calculation - in a real system, you'd track actual usage per voucher
                    remaining_balance = max(0, amount - (i * 10))  # Simplified: assume 10 credits used per transaction on average

                    # Determine status based on remaining balance
                    if remaining_balance <= 0:
                        status = "Used"
                    elif record.get('expiry', ''):
                        try:
                            from datetime import datetime
                            expiry_date = datetime.strptime(record['expiry'], '%Y-%m-%d')
                            if datetime.now() > expiry_date:
                                status = "Expired"
                            else:
                                status = "Active"
                        except:
                            status = "Active"
                    else:
                        status = "Active"

                    # Calculate days remaining until expiry
                    days_remaining_text = "No Expiry"
                    if record.get('expiry', ''):
                        try:
                            from datetime import datetime
                            expiry_date = datetime.strptime(record['expiry'], '%Y-%m-%d')
                            days_remaining = (expiry_date - datetime.now()).days
                            if days_remaining > 0:
                                days_remaining_text = f"{days_remaining} days remaining"
                            elif days_remaining == 0:
                                days_remaining_text = "Expires today"
                            else:
                                days_remaining_text = "Expired"
                        except:
                            days_remaining_text = "Unknown"

                    # Create enhanced record
                    enhanced_record = {
                        'transaction_id': transaction_id,
                        'recharge_date': redeemed_at,
                        'credit_amount': amount,
                        'expiry_date': record.get('expiry', 'No Expiry'),
                        'share_amount': record.get('share', 0),
                        'payment_method': 'Voucher Code',  # Default payment method
                        'status': status,
                        'remaining_balance': remaining_balance,
                        'remaining_date': days_remaining_text,
                        'original_record': record  # Keep original for reference
                    }

                    processed_history.append(enhanced_record)

                except Exception as e:
                    print(f"Error processing credit history record: {e}")
                    continue

            # Store the processed history
            self.credit_history = processed_history
            self.original_credit_history = processed_history.copy()

            print(f"Loaded {len(self.credit_history)} credit history records")

            # Trigger notification update after loading credit history
            self.trigger_notification_update()

        except Exception as e:
            print(f"Error loading credit history: {e}")
            self.credit_history = []
            self.original_credit_history = []

    def check_notifications(self):
        """
        Check for notification conditions and update the notifications list.
        """
        try:
            # Only check notifications if enabled in settings
            notifications_enabled = self.settings_manager.get_setting('notifications', 'enabled', True)
            if not notifications_enabled:
                self.notifications = []
                self.notification_height = 0
                return

            current_time = time.time()
            # Only check every 30 seconds to avoid excessive processing
            if current_time - self.last_notification_check < 30:
                return

            self.last_notification_check = current_time
            self.notifications = []

            # Check low credit balance condition
            show_low_credit = self.settings_manager.get_setting('notifications', 'show_low_credit_warning', True)
            if show_low_credit:
                low_credit_threshold = self.settings_manager.get_setting('notifications', 'low_credit_threshold', 500)
                current_balance = self.get_current_wallet_balance()

                if current_balance < low_credit_threshold:
                    self.notifications.append({
                        'type': 'low_credit',
                        'message': f"Warning: Credit balance is low ({current_balance:.0f} credits remaining)",
                        'priority': 1
                    })

            # Check voucher expiry conditions
            show_expiry_warning = self.settings_manager.get_setting('notifications', 'show_expiry_warning', True)
            if show_expiry_warning and hasattr(self, 'credit_history'):
                expiry_warning_days = self.settings_manager.get_setting('notifications', 'expiry_warning_days', 5)

                for record in self.credit_history:
                    # Only check active vouchers
                    if record.get('status', '').lower() == 'active':
                        expiry_date_str = record.get('expiry_date', '')
                        if expiry_date_str and expiry_date_str != 'No Expiry':
                            try:
                                from datetime import datetime
                                expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d')
                                days_remaining = (expiry_date - datetime.now()).days

                                if 0 <= days_remaining <= expiry_warning_days:
                                    transaction_id = record.get('transaction_id', 'Unknown')
                                    if days_remaining == 0:
                                        message = f"Warning: Voucher expires today (Transaction ID: {transaction_id})"
                                    else:
                                        message = f"Warning: Voucher expires in {days_remaining} days (Transaction ID: {transaction_id})"

                                    self.notifications.append({
                                        'type': 'voucher_expiry',
                                        'message': message,
                                        'priority': 2,
                                        'days_remaining': days_remaining
                                    })
                            except Exception as e:
                                print(f"Error parsing expiry date for notification: {e}")

            # Sort notifications by priority (lower number = higher priority)
            self.notifications.sort(key=lambda x: x.get('priority', 999))

            # Calculate notification area height
            if self.notifications:
                # Base height + height per notification
                base_height = int(20 * self.scale_y)  # Padding
                notification_line_height = int(25 * self.scale_y)  # Height per notification
                self.notification_height = base_height + (len(self.notifications) * notification_line_height)
            else:
                self.notification_height = 0

        except Exception as e:
            print(f"Error checking notifications: {e}")
            self.notifications = []
            self.notification_height = 0

    def draw_notifications(self, screen_width, screen_height):
        """
        Draw notification area at the bottom of the stats page.

        Args:
            screen_width: Width of the screen
            screen_height: Height of the screen
        """
        if not self.notifications:
            return

        try:
            # Calculate notification area position
            notification_y = screen_height - self.notification_height - int(10 * self.scale_y)
            notification_rect = pygame.Rect(0, notification_y, screen_width, self.notification_height)

            # Create gradient background surface
            gradient_surface = pygame.Surface((screen_width, self.notification_height), pygame.SRCALPHA)

            # Create red gradient background
            for i in range(self.notification_height):
                # Gradient from dark red at top to lighter red at bottom
                alpha = 180 + int(75 * (i / self.notification_height))  # 180-255 alpha range
                red_intensity = 120 + int(60 * (i / self.notification_height))  # 120-180 red range
                color = (red_intensity, 20, 20, alpha)
                pygame.draw.line(gradient_surface, color, (0, i), (screen_width, i))

            # Draw gradient background
            self.screen.blit(gradient_surface, (0, notification_y))

            # Draw border
            pygame.draw.rect(self.screen, (200, 50, 50), notification_rect, width=2)

            # Draw notifications
            font = self.get_font("Arial", self.scaled_font_size(12))
            line_height = int(25 * self.scale_y)
            padding_x = int(15 * self.scale_x)
            padding_y = int(10 * self.scale_y)

            for i, notification in enumerate(self.notifications):
                text_y = notification_y + padding_y + (i * line_height)

                # Render notification text in white for visibility
                text_surface = font.render(notification['message'], True, WHITE)
                self.screen.blit(text_surface, (padding_x, text_y))

                # Add warning icon
                icon_x = screen_width - int(30 * self.scale_x)
                icon_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
                icon_surface = icon_font.render("⚠", True, (255, 255, 0))  # Yellow warning icon
                self.screen.blit(icon_surface, (icon_x, text_y - int(2 * self.scale_y)))

        except Exception as e:
            print(f"Error drawing notifications: {e}")

    def draw_export_button(self, screen_width, header_height):
        """
        Draw export button in the header area, positioned near the logout button.

        Args:
            screen_width: Width of the screen
            header_height: Height of the header area
        """
        try:
            # Export button dimensions
            button_width = int(120 * self.scale_x)
            button_height = int(30 * self.scale_y)

            # Position to the left of logout button
            logout_button_width = int(80 * self.scale_x)
            spacing = int(10 * self.scale_x)
            button_x = screen_width - logout_button_width - button_width - spacing - int(20 * self.scale_x)
            button_y = int(10 * self.scale_y)

            # Store for click detection
            export_rect = pygame.Rect(button_x, button_y, button_width, button_height)
            self.export_button_rect = export_rect

            # Button color based on state
            if self.export_in_progress:
                button_color = (100, 100, 100)  # Gray when exporting
                text_color = (200, 200, 200)
            elif hasattr(self, 'export_button_hover') and self.export_button_hover:
                button_color = (80, 120, 180)   # Lighter blue on hover
                text_color = WHITE
            else:
                button_color = (60, 100, 160)   # Default blue
                text_color = WHITE

            # Draw button background
            pygame.draw.rect(self.screen, button_color, export_rect, border_radius=5)
            pygame.draw.rect(self.screen, (40, 80, 140), export_rect, width=2, border_radius=5)

            # Draw button content
            button_font = self.get_font("Arial", self.scaled_font_size(11), bold=True)

            if self.export_in_progress:
                # Show progress during export
                text = f"Exporting... {self.export_progress}%"
            else:
                # Show export icon and text
                text = "📊 Export Report"

            text_surface = button_font.render(text, True, text_color)
            text_rect = text_surface.get_rect(center=export_rect.center)
            self.screen.blit(text_surface, text_rect)

            # Draw format indicator (small text below main text)
            if not self.export_in_progress:
                format_font = self.get_font("Arial", self.scaled_font_size(8))
                format_text = f"({self.export_format.upper()})"
                format_surface = format_font.render(format_text, True, text_color)
                format_rect = format_surface.get_rect(centerx=export_rect.centerx,
                                                    y=text_rect.bottom - int(2 * self.scale_y))
                self.screen.blit(format_surface, format_rect)

        except Exception as e:
            print(f"Error drawing export button: {e}")

    def handle_export_click(self):
        """
        Handle export button click - show format selection and initiate export.
        """
        try:
            # Get default export format from settings
            default_format = self.settings_manager.get_setting('import_export', 'default_export_format', 'pdf')
            self.export_format = default_format

            # Start export process in a separate thread to avoid blocking UI
            export_thread = threading.Thread(target=self.perform_export, daemon=True)
            export_thread.start()

        except Exception as e:
            print(f"Error handling export click: {e}")
            self.show_message(f"Export error: {str(e)}", "error")

    def perform_export(self):
        """
        Perform the actual export operation in a separate thread.
        """
        try:
            self.export_in_progress = True
            self.export_progress = 0
            self.export_status = "Initializing export..."

            # Import required modules
            import os
            from datetime import datetime
            import tkinter as tk
            from tkinter import filedialog

            # Update progress
            self.export_progress = 10
            self.export_status = "Gathering data..."

            # Collect all data for export
            export_data = self.collect_export_data()

            # Update progress
            self.export_progress = 30
            self.export_status = "Generating filename..."

            # Generate filename
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M")
            filename = f"WOW_Bingo_Stats_Report_{timestamp}.{self.export_format}"

            # Update progress
            self.export_progress = 40
            self.export_status = "Selecting save location..."

            # Show file dialog in main thread
            def show_file_dialog():
                root = tk.Tk()
                root.withdraw()  # Hide the main window

                # Get default export location
                default_location = self.settings_manager.get_setting('import_export', 'export_location', '')
                if not default_location:
                    default_location = os.path.expanduser("~/Documents")

                # Show save dialog
                file_path = filedialog.asksaveasfilename(
                    title="Save Stats Report",
                    defaultextension=f".{self.export_format}",
                    filetypes=[
                        (f"{self.export_format.upper()} files", f"*.{self.export_format}"),
                        ("All files", "*.*")
                    ],
                    initialdir=default_location,
                    initialfile=filename
                )
                root.destroy()
                return file_path

            # Execute file dialog in main thread
            import pygame
            pygame.event.post(pygame.event.Event(pygame.USEREVENT + 1, {'action': 'show_file_dialog'}))

            # Wait for file dialog result (this is a simplified approach)
            # In a real implementation, you'd want to handle this more elegantly
            file_path = show_file_dialog()

            if not file_path:
                self.export_in_progress = False
                self.show_message("Export cancelled", "info")
                return

            # Update progress
            self.export_progress = 60
            self.export_status = f"Generating {self.export_format.upper()} report..."

            # Generate the report
            if self.export_format.lower() == 'pdf':
                success = self.generate_pdf_report(export_data, file_path)
            else:  # HTML
                success = self.generate_html_report(export_data, file_path)

            # Update progress
            self.export_progress = 90
            self.export_status = "Finalizing..."

            if success:
                # Update progress
                self.export_progress = 100
                self.export_status = "Export completed successfully!"

                # Auto-open file if setting is enabled
                auto_open = self.settings_manager.get_setting('import_export', 'auto_open_exported_file', True)
                if auto_open:
                    try:
                        os.startfile(file_path)  # Windows
                    except AttributeError:
                        try:
                            os.system(f'open "{file_path}"')  # macOS
                        except:
                            os.system(f'xdg-open "{file_path}"')  # Linux

                self.show_message(f"Report exported successfully to {os.path.basename(file_path)}", "success")
            else:
                self.show_message("Export failed. Please try again.", "error")

        except Exception as e:
            print(f"Error during export: {e}")
            self.show_message(f"Export error: {str(e)}", "error")
        finally:
            self.export_in_progress = False
            self.export_progress = 0
            self.export_status = ""

    def collect_export_data(self):
        """
        Collect all data needed for export based on settings.

        Returns:
            dict: Dictionary containing all export data
        """
        try:
            from datetime import datetime

            export_data = {
                'metadata': {
                    'generation_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'time_period': self.current_time_period,
                    'authenticated_user': getattr(self, 'auth_username', 'Unknown'),
                    'report_format': self.export_format
                },
                'summary_data': {},
                'game_history': [],
                'credit_history': [],
                'notifications': []
            }

            # Collect summary data if enabled
            if self.settings_manager.get_setting('import_export', 'include_summary_data', True):
                export_data['summary_data'] = {
                    'total_earning': getattr(self, 'total_earning', 0),
                    'daily_games_played': getattr(self, 'daily_games_played', 0),
                    'daily_earning': getattr(self, 'daily_earning', 0),
                    'wallet_balance': self.get_current_wallet_balance(),
                    'time_period': self.current_time_period
                }

            # Collect game history if enabled
            if self.settings_manager.get_setting('import_export', 'include_game_history', True):
                if hasattr(self, 'game_history') and self.game_history:
                    export_data['game_history'] = self.game_history.copy()
                    export_data['metadata']['game_history_count'] = len(self.game_history)

            # Collect credit history if enabled
            if self.settings_manager.get_setting('import_export', 'include_credit_history', True):
                if hasattr(self, 'credit_history') and self.credit_history:
                    export_data['credit_history'] = self.credit_history.copy()
                    export_data['metadata']['credit_history_count'] = len(self.credit_history)

            # Collect notifications if enabled
            if self.settings_manager.get_setting('import_export', 'include_notifications', True):
                if hasattr(self, 'notifications') and self.notifications:
                    export_data['notifications'] = [notif['message'] for notif in self.notifications]

            return export_data

        except Exception as e:
            print(f"Error collecting export data: {e}")
            return {}

    def generate_pdf_report(self, export_data, file_path):
        """
        Generate PDF report using the existing stats_export module.

        Args:
            export_data (dict): Data to include in the report
            file_path (str): Path where to save the PDF file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Import the existing stats export module
            from stats_export import StatsExporter

            # Create exporter instance
            exporter = StatsExporter()

            # Generate PDF report
            success = exporter.export_comprehensive_report(
                export_data=export_data,
                file_path=file_path,
                format_type='pdf'
            )

            return success

        except ImportError:
            print("Stats export module not available, using fallback PDF generation")
            return self.generate_fallback_pdf_report(export_data, file_path)
        except Exception as e:
            print(f"Error generating PDF report: {e}")
            return False

    def generate_fallback_pdf_report(self, export_data, file_path):
        """
        Fallback PDF generation using ReportLab directly.

        Args:
            export_data (dict): Data to include in the report
            file_path (str): Path where to save the PDF file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors

            # Create PDF document
            doc = SimpleDocTemplate(file_path, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                alignment=1  # Center alignment
            )
            story.append(Paragraph("WOW Bingo Stats Report", title_style))
            story.append(Spacer(1, 20))

            # Metadata
            metadata = export_data.get('metadata', {})
            story.append(Paragraph(f"<b>Generated:</b> {metadata.get('generation_date', 'Unknown')}", styles['Normal']))
            story.append(Paragraph(f"<b>Time Period:</b> {metadata.get('time_period', 'Unknown')}", styles['Normal']))
            story.append(Paragraph(f"<b>User:</b> {metadata.get('authenticated_user', 'Unknown')}", styles['Normal']))
            story.append(Spacer(1, 20))

            # Summary data
            summary_data = export_data.get('summary_data', {})
            if summary_data:
                story.append(Paragraph("Summary Statistics", styles['Heading2']))
                summary_table_data = [
                    ['Metric', 'Value'],
                    ['Total Earning', f"{summary_data.get('total_earning', 0):.1f} ETB"],
                    ['Daily Games Played', str(summary_data.get('daily_games_played', 0))],
                    ['Daily Earning', f"{summary_data.get('daily_earning', 0):.1f} ETB"],
                    ['Wallet Balance', f"{summary_data.get('wallet_balance', 0):.1f} ETB"]
                ]

                summary_table = Table(summary_table_data)
                summary_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 14),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(summary_table)
                story.append(Spacer(1, 20))

            # Build PDF
            doc.build(story)
            return True

        except Exception as e:
            print(f"Error in fallback PDF generation: {e}")
            return False

    def generate_html_report(self, export_data, file_path):
        """
        Generate HTML report with embedded CSS for professional formatting.

        Args:
            export_data (dict): Data to include in the report
            file_path (str): Path where to save the HTML file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            metadata = export_data.get('metadata', {})
            summary_data = export_data.get('summary_data', {})
            game_history = export_data.get('game_history', [])
            credit_history = export_data.get('credit_history', [])
            notifications = export_data.get('notifications', [])

            html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WOW Bingo Stats Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #2E7D32;
            margin: 0;
            font-size: 2.5em;
        }}
        .metadata {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }}
        .metadata p {{
            margin: 5px 0;
            font-weight: bold;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #1976D2;
            border-bottom: 2px solid #1976D2;
            padding-bottom: 10px;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }}
        .summary-card .value {{
            font-size: 2em;
            font-weight: bold;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        tr:hover {{
            background-color: #e8f5e8;
        }}
        .notifications {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
        }}
        .notification-item {{
            color: #856404;
            margin: 5px 0;
            font-weight: bold;
        }}
        @media print {{
            body {{ background-color: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 WOW Bingo Stats Report</h1>
        </div>

        <div class="metadata">
            <p>📅 Generated: {metadata.get('generation_date', 'Unknown')}</p>
            <p>📊 Time Period: {metadata.get('time_period', 'Unknown')}</p>
            <p>👤 User: {metadata.get('authenticated_user', 'Unknown')}</p>
            <p>📄 Format: {metadata.get('report_format', 'HTML').upper()}</p>
        </div>
"""

            # Add summary data section
            if summary_data:
                html_content += f"""
        <div class="section">
            <h2>📈 Summary Statistics</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>Total Earning</h3>
                    <div class="value">{summary_data.get('total_earning', 0):.1f} ETB</div>
                </div>
                <div class="summary-card">
                    <h3>Daily Games Played</h3>
                    <div class="value">{summary_data.get('daily_games_played', 0)}</div>
                </div>
                <div class="summary-card">
                    <h3>Daily Earning</h3>
                    <div class="value">{summary_data.get('daily_earning', 0):.1f} ETB</div>
                </div>
                <div class="summary-card">
                    <h3>Wallet Balance</h3>
                    <div class="value">{summary_data.get('wallet_balance', 0):.1f} ETB</div>
                </div>
            </div>
        </div>
"""

            # Add notifications section
            if notifications:
                html_content += """
        <div class="section">
            <h2>⚠️ Current Notifications</h2>
            <div class="notifications">
"""
                for notification in notifications:
                    html_content += f'                <div class="notification-item">• {notification}</div>\n'

                html_content += """
            </div>
        </div>
"""

            # Add game history section
            if game_history:
                html_content += f"""
        <div class="section">
            <h2>🎮 Game History ({len(game_history)} records)</h2>
            <table>
                <thead>
                    <tr>
                        <th>Session ID</th>
                        <th>Date</th>
                        <th>Pattern</th>
                        <th>Total Calls</th>
                        <th>Earning</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
"""
                for game in game_history[:50]:  # Limit to first 50 records for HTML
                    html_content += f"""
                    <tr>
                        <td>{game.get('session_id', 'N/A')}</td>
                        <td>{game.get('date', 'N/A')}</td>
                        <td>{game.get('pattern', 'N/A')}</td>
                        <td>{game.get('total_calls', 'N/A')}</td>
                        <td>{game.get('earning', 0):.1f} ETB</td>
                        <td>{game.get('status', 'N/A')}</td>
                    </tr>
"""

                html_content += """
                </tbody>
            </table>
        </div>
"""

            # Add credit history section
            if credit_history:
                html_content += f"""
        <div class="section">
            <h2>💳 Credit Recharge History ({len(credit_history)} records)</h2>
            <table>
                <thead>
                    <tr>
                        <th>Transaction ID</th>
                        <th>Recharge Date</th>
                        <th>Amount</th>
                        <th>Expiry Date</th>
                        <th>Status</th>
                        <th>Remaining Date</th>
                    </tr>
                </thead>
                <tbody>
"""
                for credit in credit_history[:50]:  # Limit to first 50 records for HTML
                    html_content += f"""
                    <tr>
                        <td>{credit.get('transaction_id', 'N/A')}</td>
                        <td>{credit.get('recharge_date', 'N/A')}</td>
                        <td>{credit.get('credit_amount', 0):.1f} ETB</td>
                        <td>{credit.get('expiry_date', 'N/A')}</td>
                        <td>{credit.get('status', 'N/A')}</td>
                        <td>{credit.get('remaining_date', 'N/A')}</td>
                    </tr>
"""

                html_content += """
                </tbody>
            </table>
        </div>
"""

            # Close HTML
            html_content += """
    </div>
</body>
</html>
"""

            # Write HTML file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            return True

        except Exception as e:
            print(f"Error generating HTML report: {e}")
            return False

    def handle_table_keyboard_navigation(self, key):
        """
        Handle keyboard navigation for the table.

        Args:
            key: Pygame key constant

        Returns:
            bool: True if the key was handled, False otherwise
        """
        if not hasattr(self, 'game_history') or not self.game_history:
            return False

        processed_history = self._group_game_history_by_date(self.game_history)
        if not processed_history:
            return False

        max_index = len(processed_history) - 1

        if key == pygame.K_DOWN:
            # Move selection down
            if self.selected_row_index < max_index:
                self.selected_row_index += 1
            else:
                self.selected_row_index = 0  # Wrap to top
            self._show_selection_info()
            return True

        elif key == pygame.K_UP:
            # Move selection up
            if self.selected_row_index > 0:
                self.selected_row_index -= 1
            else:
                self.selected_row_index = max_index  # Wrap to bottom
            self._show_selection_info()
            return True

        elif key == pygame.K_RETURN or key == pygame.K_SPACE:
            # Activate selected row (like double-click)
            if 0 <= self.selected_row_index <= max_index:
                self.handle_row_double_click(self.selected_row_index)
            return True

        elif key == pygame.K_ESCAPE:
            # Clear selection
            self.selected_row_index = -1
            self.show_message("Selection cleared", "info")
            return True

        return False

    def _show_selection_info(self):
        """Show information about the currently selected row."""
        try:
            processed_history = self._group_game_history_by_date(self.game_history)
            if 0 <= self.selected_row_index < len(processed_history):
                record = processed_history[self.selected_row_index]
                if record.get('is_date_separator', False):
                    day_name = record.get('day_name', 'Unknown')
                    self.show_message(f"Selected date section: {day_name}", "info")
                else:
                    session_id = record.get('session_id', 'N/A')
                    player_name = record.get('username', 'Unknown')
                    self.show_message(f"Selected: {session_id} - {player_name}", "info")
        except Exception as e:
            print(f"Error showing selection info: {e}")

    def highlight_search_match(self, text, query):
        """
        Create a highlighted version of text that matches the search query.

        Args:
            text: Original text
            query: Search query to highlight

        Returns:
            tuple: (highlighted_text, has_match) where highlighted_text contains
                   the text with highlighting markers and has_match is boolean
        """
        if not query or not text:
            return str(text), False

        text_str = str(text)
        query_lower = query.lower().strip()
        text_lower = text_str.lower()

        if query_lower in text_lower:
            # Find the position of the match
            start_pos = text_lower.find(query_lower)
            if start_pos != -1:
                end_pos = start_pos + len(query_lower)
                # Return text with highlighting markers
                highlighted = (text_str[:start_pos] +
                             "**" + text_str[start_pos:end_pos] + "**" +
                             text_str[end_pos:])
                return highlighted, True

        return text_str, False

    def draw_highlighted_text(self, surface, text, font, color, rect, alignment="left"):
        """
        Draw text with search highlighting.

        Args:
            surface: Surface to draw on
            text: Text with highlighting markers (**)
            font: Font to use
            color: Base text color
            rect: Rectangle to draw within
            alignment: Text alignment ("left", "center", "right")

        Returns:
            pygame.Rect: The actual rectangle used for the text
        """
        if "**" not in text:
            # No highlighting needed, draw normally
            text_surface = font.render(text, True, color)
            if alignment == "center":
                text_rect = text_surface.get_rect(center=rect.center)
            elif alignment == "right":
                text_rect = text_surface.get_rect(right=rect.right - 5, centery=rect.centery)
            else:  # left
                text_rect = text_surface.get_rect(left=rect.left + 5, centery=rect.centery)

            surface.blit(text_surface, text_rect)
            return text_rect

        # Split text by highlighting markers
        parts = text.split("**")
        current_x = rect.left + 5

        if alignment == "center":
            # Calculate total width first for centering
            total_width = 0
            for i, part in enumerate(parts):
                if part:  # Skip empty parts
                    part_surface = font.render(part, True, color)
                    total_width += part_surface.get_width()
            current_x = rect.centerx - total_width // 2
        elif alignment == "right":
            # Calculate total width first for right alignment
            total_width = 0
            for i, part in enumerate(parts):
                if part:  # Skip empty parts
                    part_surface = font.render(part, True, color)
                    total_width += part_surface.get_width()
            current_x = rect.right - 5 - total_width

        # Draw each part
        for i, part in enumerate(parts):
            if not part:  # Skip empty parts
                continue

            # Determine if this part should be highlighted
            is_highlighted = i % 2 == 1  # Odd indices are highlighted

            if is_highlighted:
                # Draw highlighted text with background
                highlight_color = (255, 255, 0)  # Yellow highlight
                text_color = (0, 0, 0)  # Black text on yellow

                part_surface = font.render(part, True, text_color)
                part_rect = pygame.Rect(current_x, rect.centery - part_surface.get_height()//2,
                                      part_surface.get_width(), part_surface.get_height())

                # Draw highlight background
                pygame.draw.rect(surface, highlight_color, part_rect)
                surface.blit(part_surface, part_rect)
            else:
                # Draw normal text
                part_surface = font.render(part, True, color)
                part_rect = pygame.Rect(current_x, rect.centery - part_surface.get_height()//2,
                                      part_surface.get_width(), part_surface.get_height())
                surface.blit(part_surface, part_rect)

            current_x += part_surface.get_width()

        return pygame.Rect(rect.left + 5, rect.centery - font.get_height()//2,
                          current_x - rect.left - 5, font.get_height())

    def perform_search(self, query):
        """
        Perform search across game history data.

        Args:
            query: Search query string

        Returns:
            List of filtered game records
        """
        if not query or not self.original_game_history:
            return self.original_game_history.copy()

        query_lower = query.lower().strip()
        if not query_lower:
            return self.original_game_history.copy()

        filtered_results = []

        for game in self.original_game_history:
            # Search across multiple columns
            searchable_fields = [
                str(game.get('id', '')),  # Original ID for session matching
                str(game.get('username', '')),  # Player name
                str(game.get('house', '')),  # Game pattern
                str(game.get('status', '')),  # Game status
                str(game.get('date_time', '')),  # Date/time
                str(game.get('stake', '')),  # Stake amount
                str(game.get('total_prize', '')),  # Prize pool
                str(game.get('fee', '')),  # Commission
            ]

            # Check if query matches any field
            match_found = False
            for field_value in searchable_fields:
                if query_lower in field_value.lower():
                    match_found = True
                    break

            # Also check for session ID format (Session-X)
            if not match_found:
                # Generate session ID for this game based on its position in date group
                try:
                    game_date = game.get('date_time', '')
                    if game_date:
                        from datetime import datetime
                        dt = datetime.strptime(game_date, '%Y-%m-%d %H:%M:%S')
                        date_key = dt.strftime('%Y-%m-%d')

                        # Find position within the same date
                        same_date_games = [g for g in self.original_game_history
                                         if g.get('date_time', '').startswith(date_key)]
                        same_date_games.sort(key=lambda x: x.get('date_time', ''), reverse=True)  # FIXED: Latest first order

                        total_games = len(same_date_games)
                        for i, same_game in enumerate(same_date_games):
                            if same_game.get('id') == game.get('id'):
                                # Reverse the session numbering: latest game gets highest number
                                session_number = total_games - i
                                session_id = f"Session-{session_number}"
                                if query_lower in session_id.lower():
                                    match_found = True
                                break
                except Exception as e:
                    print(f"Error generating session ID for search: {e}")

            if match_found:
                filtered_results.append(game)

        return filtered_results

    def update_search_results(self):
        """Update search results and refresh the display."""
        try:
            # Perform the search
            self.search_results = self.perform_search(self.search_query)

            # Update the displayed game history
            if self.search_query.strip():
                self.game_history = self.search_results
            else:
                self.game_history = self.original_game_history.copy()

            # Reset pagination to first page
            self.history_page = 0

            # Update total pages based on filtered results
            if self.game_history:
                self.total_history_pages = max(1, (len(self.game_history) + self.history_page_size - 1) // self.history_page_size)
            else:
                self.total_history_pages = 1

            # Clear selection when search results change
            self.selected_row_index = -1
            self.hovered_row_index = -1

            # Show search result count
            if self.search_query.strip():
                result_count = len(self.search_results)
                if result_count == 0:
                    self.show_message("No results found", "info")
                elif result_count == 1:
                    self.show_message("1 result found", "success")
                else:
                    self.show_message(f"{result_count} results found", "success")

        except Exception as e:
            print(f"Error updating search results: {e}")
            self.show_message("Error performing search", "error")

    def clear_search(self):
        """Clear the search query and reset to original data."""
        self.search_query = ""
        self.search_active = False
        self.game_history = self.original_game_history.copy()
        self.history_page = 0

        # Recalculate total pages
        if self.game_history:
            self.total_history_pages = max(1, (len(self.game_history) + self.history_page_size - 1) // self.history_page_size)
        else:
            self.total_history_pages = 1

        # Clear selection
        self.selected_row_index = -1
        self.hovered_row_index = -1

        self.show_message("Search cleared", "info")

    def toggle_credit_history_visibility(self):
        """Toggle the visibility of the credit recharge history section."""
        self.credit_history_visible = not self.credit_history_visible

        if self.credit_history_visible:
            # Refresh credit history when showing
            self.load_credit_history()
            self.show_message("Credit history section opened", "info")
        else:
            self.show_message("Credit history section closed", "info")

    def perform_credit_search(self, query):
        """
        Perform search across credit recharge history data.

        Args:
            query: Search query string

        Returns:
            List of filtered credit records
        """
        if not query or not self.original_credit_history:
            return self.original_credit_history.copy()

        query_lower = query.lower().strip()
        if not query_lower:
            return self.original_credit_history.copy()

        filtered_results = []

        for record in self.original_credit_history:
            # Search across multiple columns
            searchable_fields = [
                str(record.get('transaction_id', '')),
                str(record.get('recharge_date', '')),
                str(record.get('credit_amount', '')),
                str(record.get('expiry_date', '')),
                str(record.get('share_amount', '')),
                str(record.get('payment_method', '')),
                str(record.get('status', '')),
                str(record.get('remaining_balance', '')),
            ]

            # Check if query matches any field
            match_found = False
            for field_value in searchable_fields:
                if query_lower in field_value.lower():
                    match_found = True
                    break

            if match_found:
                filtered_results.append(record)

        return filtered_results

    def update_credit_search_results(self):
        """Update credit search results and refresh the display."""
        try:
            # Perform the search
            self.credit_search_results = self.perform_credit_search(self.credit_search_query)

            # Update the displayed credit history
            if self.credit_search_query.strip():
                self.credit_history = self.credit_search_results
            else:
                self.credit_history = self.original_credit_history.copy()

            # Clear selection when search results change
            self.credit_selected_row_index = -1
            self.credit_hovered_row_index = -1

            # Show search result count
            if self.credit_search_query.strip():
                result_count = len(self.credit_search_results)
                if result_count == 0:
                    self.show_message("No credit history results found", "info")
                elif result_count == 1:
                    self.show_message("1 credit history result found", "success")
                else:
                    self.show_message(f"{result_count} credit history results found", "success")

        except Exception as e:
            print(f"Error updating credit search results: {e}")
            self.show_message("Error performing credit search", "error")

    def clear_credit_search(self):
        """Clear the credit search query and reset to original data."""
        self.credit_search_query = ""
        self.credit_search_active = False
        self.credit_history = self.original_credit_history.copy()

        # Clear selection
        self.credit_selected_row_index = -1
        self.credit_hovered_row_index = -1

        self.show_message("Credit search cleared", "info")

    def handle_search_input(self, event):
        """
        Handle text input for search functionality.

        Args:
            event: Pygame event object

        Returns:
            bool: True if the event was handled, False otherwise
        """
        if not self.search_active:
            return False

        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_BACKSPACE:
                # Remove last character
                if self.search_query:
                    self.search_query = self.search_query[:-1]
                    self.search_debounce_timer = pygame.time.get_ticks()
                return True
            elif event.key == pygame.K_RETURN:
                # Perform immediate search
                self.update_search_results()
                return True
            elif event.key == pygame.K_ESCAPE:
                # Clear search and deactivate
                self.clear_search()
                return True
        elif event.type == pygame.TEXTINPUT:
            # Add typed character
            if len(self.search_query) < 50:  # Limit search query length
                self.search_query += event.text
                self.search_debounce_timer = pygame.time.get_ticks()
            return True

        return False

    def init_button_animations(self):
        """Initialize button animation states"""
        # Add animation states for all buttons
        button_ids = [
            "nav_play", "nav_stats", "nav_settings", "nav_help",
            "back_button"
        ]

        for btn_id in button_ids:
            self.button_states[btn_id] = {
                "hover": False,
                "click": False,
                "click_time": 0,
                "hover_alpha": 0
            }

    def load_sound_effects(self):
        """Load sound effects if enabled in settings"""
        self.button_click_sound = None

        # Check if sound effects are enabled in settings
        audio_settings = self.settings.get("audio", {})
        if isinstance(audio_settings, dict) and audio_settings.get("sound_effects_enabled", True):
            try:
                volume = audio_settings.get("sound_effects_volume", 0.7) if isinstance(audio_settings, dict) else 0.7
                self.button_click_sound = pygame.mixer.Sound("assets/sounds/button_click.wav")
                self.button_click_sound.set_volume(volume)
            except:
                print("Could not load sound effects")




    def get_cached_display_state(self, section_name):
        """FINAL FIX: Get cached display state to prevent re-evaluation every frame."""
        current_time = time.time()
        
        # Check if we have a valid cached state
        if (section_name in self._display_state_cache and 
            current_time - self._last_display_evaluation < self._display_cache_timeout):
            return self._display_state_cache[section_name]
        
        # Need to evaluate - but only do this once per second maximum
        return None
    
    def set_cached_display_state(self, section_name, state):
        """FINAL FIX: Cache display state to prevent blinking."""
        self._display_state_cache[section_name] = state
        self._last_display_evaluation = time.time()
    
    def invalidate_display_cache(self, reason="data_changed"):
        """FINAL FIX: Invalidate display cache when data actually changes."""
        self._display_state_cache.clear()
        self._last_display_evaluation = 0
        print(f"FINAL FIX: Display cache invalidated - {reason}")
    
    def should_render_section(self, section_name):
        """ANTI-BLINK: Check if a section should be rendered to prevent rapid updates."""
        if not hasattr(self, '_last_render_time'):
            self._last_render_time = {}
        if not hasattr(self, '_render_cooldown'):
            self._render_cooldown = 1.0
        
        current_time = time.time()
        last_render = self._last_render_time.get(section_name, 0)
        
        if current_time - last_render >= self._render_cooldown:
            self._last_render_time[section_name] = current_time
            return True
        return False
    
    def should_update_section(self, section_name):
        """CRITICAL FIX: Check if a section should be updated to prevent blinking."""
        current_time = time.time()
        last_update = self._last_section_update.get(section_name, 0)
        
        if current_time - last_update >= self._section_update_cooldown:
            self._last_section_update[section_name] = current_time
            return True
        return False
    
    def set_stable_ui_data(self, section_name, data):
        """CRITICAL FIX: Set stable UI data to prevent flickering."""
        self._stable_ui_state[section_name] = data
    
    def get_stable_ui_data(self, section_name, default=None):
        """CRITICAL FIX: Get stable UI data to prevent flickering."""
        return self._stable_ui_state.get(section_name, default)
        

    def set_loading_state(self, loading=False):
        """FLICKERING FIX: Stable loading state management without forced overrides."""
        # FLICKERING FIX: Only set loading state if it's actually changing
        if hasattr(self, 'stats_loading_in_progress') and self.stats_loading_in_progress != loading:
            self.stats_loading_in_progress = loading
            if not loading:
                self.initial_loading_complete = True
            print(f"FLICKERING FIX: Loading state changed to {loading}")
        elif not hasattr(self, 'stats_loading_in_progress'):
            self.stats_loading_in_progress = loading
            self.initial_loading_complete = not loading
    
    def _show_loading_indicator(self, message="Loading..."):
        """FLICKERING FIX: Completely disabled loading indicator to prevent blinking."""
        # FLICKERING FIX: Loading indicator completely disabled to prevent continuous blinking
        # UI updates happen instantly without loading indicators
        pass
        
    def run(self):
        """Main loop for the stats page - optimized for lower CPU usage"""
        clock = pygame.time.Clock()

        # Track if we need to redraw the screen
        needs_redraw = True

        # Track if mouse has moved
        last_mouse_pos = pygame.mouse.get_pos()

        # Track time for adaptive frame rate
        last_update_time = pygame.time.get_ticks()
        update_interval = 100  # Update UI every 100ms when idle

        # If in integrated mode, just draw once and return control to the main application
        if self.integrated_mode:
            try:
                self.update()
            except Exception as e:
                print(f"Error in stats page update(): {e}")
                import traceback
                traceback.print_exc()
                return

            try:
                self.draw()
            except Exception as e:
                print(f"Error in stats page draw(): {e}")
                import traceback
                traceback.print_exc()
                return
            # Don't call pygame.display.flip() here - let the main application handle it
            return

        # Standalone mode - run in a loop
        while self.running:
            current_time = pygame.time.get_ticks()

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    pygame.quit()
                    sys.exit()
                elif event.type == pygame.USEREVENT:
                    # Handle refresh_stats events
                    if hasattr(event, 'stats_type') and event.stats_type == 'refresh_stats':
                        if self.handle_refresh_event(event):
                            needs_redraw = True
                    # Handle wallet balance update events
                    elif hasattr(event, 'wallet_balance_update'):
                        self.refresh_wallet_balance()
                        needs_redraw = True
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    # Check if we have a handle_event method (added by payment integration)
                    if hasattr(self, 'handle_event') and callable(self.handle_event):
                        if self.handle_event(event):
                            needs_redraw = True
                            continue

                    # Check if we're on login screen
                    if not self.is_authenticated():
                        if self.handle_login_mouse_click(event.pos):
                            needs_redraw = True
                    else:
                        # Fall back to our own handler
                        if self.handle_mouse_click(event.pos):
                            needs_redraw = True
                elif event.type == pygame.MOUSEMOTION:
                    current_mouse_pos = event.pos
                    # Only process motion if the mouse has moved significantly
                    if (abs(current_mouse_pos[0] - last_mouse_pos[0]) > 2 or
                        abs(current_mouse_pos[1] - last_mouse_pos[1]) > 2):
                        # Check if we're on login screen
                        if not self.is_authenticated():
                            if self.handle_login_mouse_motion(current_mouse_pos):
                                needs_redraw = True
                        else:
                            # Handle mouse motion and check if redraw is needed
                            motion_needs_redraw = self.handle_mouse_motion(current_mouse_pos)
                            if motion_needs_redraw:
                                needs_redraw = True
                        last_mouse_pos = current_mouse_pos
                elif event.type == pygame.MOUSEWHEEL:
                    # Handle mouse wheel scrolling
                    if self.handle_mouse_wheel(event.y):
                        needs_redraw = True
                elif event.type == pygame.KEYDOWN:
                    # Check if we're on login screen
                    if not self.is_authenticated():
                        if self.handle_login_input(event):
                            needs_redraw = True
                    else:
                        # PRIORITY 1: Check if we have a handle_event method (added by payment integration)
                        # This must come FIRST to handle recharge UI keyboard events
                        if hasattr(self, 'handle_event') and callable(self.handle_event):
                            if self.handle_event(event):
                                needs_redraw = True
                                continue

                        # PRIORITY 2: Check for search input handling (only if payment integration didn't handle it)
                        if self.handle_search_input(event):
                            needs_redraw = True
                            continue

                        # PRIORITY 3: Check for table keyboard navigation
                        if self.handle_table_keyboard_navigation(event.key):
                            needs_redraw = True
                            continue

                        # PRIORITY 4: Default escape key handling
                        if event.key == pygame.K_ESCAPE:
                            self.running = False
                            if self.on_close_callback:
                                self.on_close_callback()

                    needs_redraw = True
                elif event.type == pygame.TEXTINPUT:
                    # Check if we have a handle_event method (added by payment integration) for TEXTINPUT
                    if hasattr(self, 'handle_event') and callable(self.handle_event):
                        if self.handle_event(event):
                            needs_redraw = True
                            continue

                    # Handle text input for search (only if not handled by payment integration)
                    if self.handle_search_input(event):
                        needs_redraw = True

            # Update UI only when needed or on timer
            if needs_redraw or current_time - last_update_time >= update_interval:
                # Update UI
                self.update()

                # Check if any animations are active after update
                if hasattr(self, 'animations_active') and self.animations_active:
                    needs_redraw = True

                # Draw everything
                self.draw()

                # Update display
                pygame.display.flip()

                # Reset flags
                needs_redraw = False
                last_update_time = current_time

            # Adaptive frame rate - use lower rate when idle to reduce CPU usage
            if hasattr(self, 'animations_active') and self.animations_active:
                # Use higher frame rate when animations are active
                clock.tick(60)
            elif needs_redraw:
                # Use medium frame rate when redrawing but no animations
                clock.tick(45)
            else:
                # Use very low frame rate when completely idle
                clock.tick(15)

                # Small sleep to further reduce CPU usage when idle
                pygame.time.wait(10)

    def _refresh_stats_fast(self):
        """PERFORMANCE FIX: Fast refresh without heavy database operations"""
        try:
            print("PERFORMANCE: Starting fast stats refresh")
            start_time = time.time()

            # PERFORMANCE FIX: Clear only memory caches, not persistent ones
            if hasattr(self, 'stats_provider') and hasattr(self.stats_provider, 'clear_cache'):
                self.stats_provider.clear_cache()

            # PERFORMANCE FIX: Quick data reload with timeout
            self._quick_database_load(1.0)  # 1 second timeout

            # PERFORMANCE FIX: Update cache
            self._save_to_cache()

            elapsed = time.time() - start_time
            print(f"PERFORMANCE: Fast refresh completed in {elapsed:.2f}s")

            # Show success message
            self.show_message("Statistics refreshed", "success")

        except Exception as e:
            print(f"PERFORMANCE: Fast refresh error: {e}")
            self.show_message("Refresh completed", "info")
        finally:
            # PERFORMANCE FIX: Always clear refresh flag
            self._refresh_in_progress = False

    def update(self, *args):
        """FLICKERING FIX: Stable update method without loading state conflicts

        Args:
            *args: Variable length argument list to support integration with main application
        """
        # FLICKERING FIX: Handle pygame timer events for clearing flags
        current_time = pygame.time.get_ticks()

        # Check for timer events to clear loading flags
        for event in pygame.event.get([pygame.USEREVENT + 1, pygame.USEREVENT + 2]):
            if event.type == pygame.USEREVENT + 1:
                # Clear loading flag
                self.stats_loading_in_progress = False
            elif event.type == pygame.USEREVENT + 2:
                # Clear refresh flag
                self._refresh_in_progress = False

        # FLICKERING FIX: Stable loading state management
        # Don't force loading state to False - let it be managed by the loading process

        # Update message timer
        if self.message_timer > 0:
            self.message_timer -= 1

        # Update button animations
        self.update_button_animations()

        # FLICKERING FIX: Reduced frequency wallet balance updates (every 30 seconds)
        if not hasattr(self, '_last_balance_update'):
            self._last_balance_update = current_time
        elif current_time - self._last_balance_update > 30000:  # 30 seconds
            # Update wallet balance from the payment system
            try:
                self.wallet_balance = self.get_current_wallet_balance()
                self._last_balance_update = current_time
            except Exception as e:
                print(f"Error updating wallet balance: {e}")

        # Handle search debouncing
        if self.search_debounce_timer > 0:
            if current_time - self.search_debounce_timer >= self.search_debounce_delay:
                # Perform delayed search
                self.update_search_results()
                self.search_debounce_timer = 0

        # Check notifications periodically (every 60 seconds)
        if current_time - getattr(self, '_last_notification_update', 0) > 60000:  # 60 seconds
            self.check_notifications()
            self._last_notification_update = current_time

    def update_button_animations(self):
        """Update button hover and click animations - optimized version"""
        current_time = pygame.time.get_ticks()
        mouse_pos = pygame.mouse.get_pos()

        # Track if any animations are active
        self.animations_active = False

        # Only update buttons that are visible or have active animations
        for btn_id, btn_state in self.button_states.items():
            # Skip if button doesn't have a hit area
            if btn_id not in self.hit_areas:
                continue

            # Check if this button has active animations
            has_active_animation = btn_state["click"] or btn_state["hover_alpha"] > 0

            # Check if mouse is hovering over button
            hover = self.hit_areas[btn_id].collidepoint(mouse_pos)

            # If not hovering and no active animations, skip this button
            if not hover and not has_active_animation:
                continue

            # Update click animation
            if btn_state["click"]:
                # If click animation has been playing for 200ms, end it
                if current_time - btn_state["click_time"] > 200:
                    btn_state["click"] = False
                else:
                    self.animations_active = True

            # Animate hover alpha
            if hover and btn_state["hover_alpha"] < 255:
                # Fade in (faster)
                btn_state["hover_alpha"] = min(255, btn_state["hover_alpha"] + 25)
                self.animations_active = True
            elif not hover and btn_state["hover_alpha"] > 0:
                # Fade out (slower)
                btn_state["hover_alpha"] = max(0, btn_state["hover_alpha"] - 15)
                self.animations_active = True

    @time_operation("draw_stats_page")
    def draw(self):
        """Draw the stats page - modern version based on reference image"""
        # Performance monitoring
        performance_start = time.time()
        frame_count = getattr(self, '_frame_count', 0) + 1
        self._frame_count = frame_count

        # BLINKING FIX: Intelligent frame skipping - only skip truly expensive background operations
        # Essential UI elements (game history, notifications, etc.) are always drawn
        # Only skip expensive background calculations like complex animations or heavy data processing
        skip_heavy_calculations = (frame_count % 3 == 0)  # Skip heavy calculations every 3rd frame

        # PERFORMANCE: Additional optimizations for smooth rendering
        skip_decorative_animations = (frame_count % 4 == 0)  # Skip decorative animations every 4th frame
        skip_background_updates = (frame_count % 5 == 0)  # Skip background pattern updates every 5th frame
        # Get current screen dimensions
        screen_width, screen_height = self.screen.get_size()

        # Draw background
        self.draw_background(screen_width, screen_height)

        # Check authentication status
        if not self.is_authenticated():
            self.draw_login_screen(screen_width, screen_height)
            return

        # Draw navigation bar at the top
        self.draw_navigation_bar(screen_width, screen_height)

        # Draw WOW BINGO header and get the header height
        header_height = self.draw_wow_bingo_header()

        # Draw logout button in the header
        self.draw_logout_button(screen_width, header_height)

        # Draw export button in the header
        self.draw_export_button(screen_width, header_height)

        # Calculate layout for stats sections with scroll offset
        content_start_y = header_height + int(20 * self.scale_y) - self.scroll_y

        # Store layout parameters for reuse
        self._layout = {
            'screen_width': screen_width,
            'screen_height': screen_height,
            'content_start_y': content_start_y
        }

        # Calculate total content height for scrolling (properly sized for all content)
        estimated_content_height = (
            int(50 * self.scale_y) +   # Time period tabs
            int(60 * self.scale_y) +   # Weekly earnings (compact)
            int(70 * self.scale_y) +   # Summary cards
            int(45 * self.scale_y) +   # Search bar
            int(300 * self.scale_y) +  # Game history table (increased for proper display)
            int(250 * self.scale_y) +  # Credit history section (increased for full display)
            int(50 * self.scale_y)     # Extra padding for scrolling
        )

        # FIXED: Update scroll limits with proper calculations to ensure all content is accessible
        self.content_height = estimated_content_height
        available_height = screen_height - header_height - self.notification_height
        # Add extra padding to ensure the last section is fully scrollable
        self.max_scroll_y = max(0, self.content_height - available_height + int(50 * self.scale_y))

        # Only draw content that's visible on screen
        visible_top = self.scroll_y
        visible_bottom = self.scroll_y + screen_height

        # Draw time period selection tabs (compact spacing)
        tabs_y = content_start_y
        if tabs_y + int(40 * self.scale_y) > 0 and tabs_y < screen_height:
            tabs_height = self.draw_time_period_tabs(tabs_y)
        else:
            tabs_height = int(40 * self.scale_y)

        # Draw weekly earnings cards (compact spacing)
        weekly_y = content_start_y + tabs_height + int(5 * self.scale_y)
        if weekly_y + int(60 * self.scale_y) > 0 and weekly_y < screen_height:
            self.draw_time_period_data(weekly_y)

        # Calculate position for summary cards (compact spacing)
        summary_cards_y = weekly_y + int(60 * self.scale_y) + int(8 * self.scale_y)
        if summary_cards_y + int(70 * self.scale_y) > 0 and summary_cards_y < screen_height:
            # Draw summary cards (Total Earning, Daily Games Played, Daily Earning, Wallet Balance)
            self.draw_summary_cards(summary_cards_y)

        # Calculate position for search bar (compact layout)
        search_y = summary_cards_y + int(70 * self.scale_y) + int(10 * self.scale_y)
        if search_y + int(50 * self.scale_y) > 0 and search_y < screen_height:
            # Draw search bar (compact)
            search_bar_height = self.draw_search_bar(search_y)
        else:
            search_bar_height = int(50 * self.scale_y)

        # Calculate position for game history table (compact spacing)
        history_y = search_y + search_bar_height + int(5 * self.scale_y)
        if history_y + int(250 * self.scale_y) > 0 and history_y < screen_height:
            # BLINKING FIX: Always draw game history table - it's essential UI, not expensive
            # The blinking was caused by frame skipping that made this section appear/disappear
            self.draw_game_history(history_y)
            game_history_height = int(250 * self.scale_y)
        else:
            game_history_height = int(250 * self.scale_y)

        # Calculate position for credit history section (proper spacing)
        credit_history_y = history_y + game_history_height + int(15 * self.scale_y)
        # BLINKING FIX: Always draw credit recharge history section - essential UI component
        # The blinking was caused by frame skipping that made this section appear/disappear
        self.draw_credit_history_section(credit_history_y, screen_height)

        # CRITICAL FIX: Disable continuous loading indicator to prevent blinking
        # Loading indicator removed to prevent continuous blinking
        # Data loads in background without blocking UI

        # Draw scroll indicator if content is scrollable
        if self.max_scroll_y > 0:
            self.draw_scroll_indicator(screen_width, screen_height)

        # Draw notifications at the bottom of the page
        # BLINKING FIX: Always draw notifications - they are essential UI feedback
        self.draw_notifications(screen_width, screen_height)

        # Draw toast message if active
        if self.message_timer > 0:
            self.draw_toast_message()

    def draw_weekly_earnings(self, start_y):
        """CENTRALIZED: Draw weekly earnings cards using unified data source"""
        screen_width = self._layout['screen_width']

        # Calculate card dimensions (compact and properly fitted)
        total_margin = int(40 * self.scale_x)  # Left and right margins
        card_spacing = int(6 * self.scale_x)  # Further reduced spacing
        available_width = screen_width - total_margin
        total_spacing = 7 * card_spacing  # 7 spaces between 8 cards
        card_width = int((available_width - total_spacing) / 8)  # 8 cards total
        card_height = int(60 * self.scale_y)  # Reduced from 80 to 60

        # PERFORMANCE: Use cached weekly stats or fallback data to prevent UI blocking
        if hasattr(self, 'weekly_stats') and self.weekly_stats:
            weekly_stats = self.weekly_stats
            print(f"PERFORMANCE: Using cached weekly stats with {len(weekly_stats)} days")
        else:
            # PERFORMANCE: Use fallback data instead of blocking database call
            from datetime import datetime, timedelta
            today = datetime.now()
            weekly_stats = []

            # FIXED: Generate correct date range for current week (last 7 days including today)
            for i in range(7):
                day = today - timedelta(days=6-i)  # This gives us 7 days ending today
                weekly_stats.append({
                    'date': day.strftime('%Y-%m-%d'),
                    'games_played': 0,
                    'earnings': 0.0
                })

            print(f"PERFORMANCE: Using fallback weekly stats for dates {weekly_stats[0]['date']} to {weekly_stats[-1]['date']}")
            print(f"PERFORMANCE: Today is {today.strftime('%Y-%m-%d')}, showing last 7 days")

        # FIXED: Generate actual dates instead of using static day names
        for i in range(len(weekly_stats)):
            # Calculate card position
            card_x = int(20 * self.scale_x) + i * (card_width + card_spacing)
            card_y = start_y

            # CENTRALIZED: Get day data from unified source
            if i < len(weekly_stats):
                day_data = weekly_stats[i]
                day_earnings = day_data.get('earnings', 0)
                day_date = day_data.get('date', 'Unknown')

                # FIXED: Convert date to proper display format (like daily earnings)
                try:
                    from datetime import datetime
                    dt = datetime.strptime(day_date, '%Y-%m-%d')
                    # Use format like "Mon 12/01" for better readability
                    display_name = dt.strftime('%a %m/%d')
                except:
                    display_name = f"Day{i+1}"

                print(f"CENTRALIZED: {display_name} ({day_date}): {day_earnings:.2f} ETB")
            else:
                day_earnings = 0
                day_date = 'Unknown'
                display_name = f"Day{i+1}"
                print(f"CENTRALIZED: {display_name}: No data available")

            # Draw day card with actual date and remainder
            color = self.day_colors[i] if i < len(self.day_colors) else (100, 150, 200)
            day_remainder = self.calculate_remainder_for_date(day_date)
            self.draw_day_card(card_x, card_y, card_width, card_height, display_name, day_earnings, color, 
                             remainder=day_remainder, date_str=day_date)

        # CENTRALIZED: Calculate and draw total card
        total_x = int(20 * self.scale_x) + 7 * (card_width + card_spacing)
        total_y = start_y

        # CENTRALIZED: Calculate total earnings from unified data
        total_earnings = sum(day.get('earnings', 0) for day in weekly_stats)
        print(f"CENTRALIZED: Total weekly earnings: {total_earnings:.2f} ETB")
        
        # Calculate total remainder for the week
        weekly_remainder = self.calculate_weekly_remainder(weekly_stats)

        # Draw total card with black background and weekly remainder
        self.draw_day_card(total_x, total_y, card_width, card_height, "Total", total_earnings, 
                         TOTAL_CARD_BG, is_total=True, remainder=weekly_remainder)

    def draw_day_card(self, x, y, width, height, day_name, earnings, color, is_total=False, remainder=None, date_str=None):
        """Draw a day card with earnings and remainder - compact and properly formatted"""
        # Draw card background
        card_rect = pygame.Rect(x, y, width, height)

        # Use different style for total card
        if is_total:
            pygame.draw.rect(self.screen, color, card_rect, border_radius=5)
            # Total card uses white text
            day_text_color = WHITE
            earnings_text_color = WHITE
            remainder_text_color = (255, 165, 0)  # Orange for remainder
        else:
            # Draw card with white background and colored header
            pygame.draw.rect(self.screen, WHITE, card_rect, border_radius=5)

            # Draw colored header (compact)
            header_height = int(20 * self.scale_y)  # Reduced from 30 to 20
            header_rect = pygame.Rect(x, y, width, header_height)
            pygame.draw.rect(self.screen, color, header_rect, border_radius=5)
            pygame.draw.rect(self.screen, color, pygame.Rect(x, y + header_height - 3, width, 6))

            # Regular card uses white text for day name and green for earnings
            day_text_color = WHITE
            earnings_text_color = ETB_GREEN
            remainder_text_color = (255, 165, 0)  # Orange for remainder

        # Draw day name (compact font)
        day_font = self.get_font("Arial", self.scaled_font_size(11), bold=True)  # Reduced from 14 to 11
        day_text = day_font.render(day_name, True, day_text_color)
        day_rect = day_text.get_rect(centerx=x + width//2, y=y + int(4 * self.scale_y))  # Reduced padding
        self.screen.blit(day_text, day_rect)

        # Format earnings properly to prevent overflow
        try:
            earnings_value = float(earnings) if earnings else 0
            # Format with proper number formatting
            if earnings_value >= 1000000:
                formatted_earnings = f"{earnings_value/1000000:.1f}M"
            elif earnings_value >= 1000:
                formatted_earnings = f"{earnings_value/1000:.1f}K"
            elif earnings_value >= 100:
                formatted_earnings = f"{earnings_value:.0f}"
            elif earnings_value >= 10:
                formatted_earnings = f"{earnings_value:.1f}"
            else:
                formatted_earnings = f"{earnings_value:.1f}"
        except (ValueError, TypeError):
            formatted_earnings = "0"

        # Draw earnings (compact font and positioning)
        earnings_font = self.get_font("Arial", self.scaled_font_size(12), bold=True)  # Reduced from 16 to 12
        earnings_text = earnings_font.render(f"{formatted_earnings} ETB", True, earnings_text_color)

        # Ensure text fits within card width
        if earnings_text.get_width() > width - 10:
            # If text is too wide, use even smaller font
            earnings_font = self.get_font("Arial", self.scaled_font_size(10), bold=True)
            earnings_text = earnings_font.render(f"{formatted_earnings} ETB", True, earnings_text_color)

        earnings_rect = earnings_text.get_rect(centerx=x + width//2, y=y + int(30 * self.scale_y))  # Adjusted position
        self.screen.blit(earnings_text, earnings_rect)
        
        # Calculate and draw remainder if not provided
        if remainder is None and date_str:
            remainder = self.calculate_remainder_for_date(date_str)
        
        # Draw remainder if available
        if remainder is not None:
            remainder_font = self.get_font("Arial", self.scaled_font_size(10), bold=True)
            remainder_text = remainder_font.render(f"R: {remainder}", True, remainder_text_color)
            remainder_rect = remainder_text.get_rect(centerx=x + width//2, y=y + int(48 * self.scale_y))
            self.screen.blit(remainder_text, remainder_rect)

    def draw_summary_cards(self, start_y):
        """Draw summary cards (Total Earning, Daily Games Played, Daily Earning, Wallet Balance)"""
        screen_width = self._layout['screen_width']

        # Calculate card dimensions (optimized for 4 cards, removing UUID card for space)
        card_width = int(screen_width / 4) - int(20 * self.scale_x)  # 4 cards for better spacing
        card_height = int(70 * self.scale_y)  # Slightly reduced height
        card_spacing = int(20 * self.scale_x)  # Better spacing

        # Get current wallet balance (always fetch the latest value)
        current_wallet_balance = self.get_current_wallet_balance()

        # PERFORMANCE: Use cached data to prevent UI blocking during draw operations
        today_date = datetime.now().strftime('%Y-%m-%d')

        # Use stored values from background loading or defaults
        actual_daily_earnings = getattr(self, 'daily_earnings', 0.0)
        actual_daily_games = getattr(self, 'daily_games', 0)
        total_earnings = getattr(self, 'total_earnings', 0.0)

        # Debug logging for verification
        print(f"PERFORMANCE: Summary cards - Daily games: {actual_daily_games}, Total earnings: {total_earnings:.1f}, Daily earnings: {actual_daily_earnings:.1f}")
        print(f"PERFORMANCE: Using cached data to prevent UI blocking")

        # Card data (removed UUID card to save space)
        cards = [
            {"title": "TOTAL EARNING", "value": f"{total_earnings:.1f}", "suffix": " ETB"},
            {"title": "Daily GAMES PLAYED", "value": f"{actual_daily_games}", "suffix": ""},
            {"title": "Daily Earning", "value": f"{actual_daily_earnings:.1f}", "suffix": " ETB"},
            {"title": "WALLET BALANCE", "value": f"{current_wallet_balance:.1f}", "suffix": " ETB"}
        ]

        # Draw each card
        for i, card in enumerate(cards):
            # Calculate card position
            card_x = int(20 * self.scale_x) + i * (card_width + card_spacing)
            card_y = start_y

            # Draw card
            self.draw_summary_card(card_x, card_y, card_width, card_height, card["title"], card["value"], card["suffix"])

    def draw_summary_card(self, x, y, width, height, title, value, suffix):
        """Draw a summary card with title and value"""
        # Draw card background
        card_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(self.screen, CARD_BG, card_rect, border_radius=5)

        # Draw title
        title_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        title_text = title_font.render(title, True, WHITE)
        title_rect = title_text.get_rect(x=x + int(15 * self.scale_x), y=y + int(10 * self.scale_y))
        self.screen.blit(title_text, title_rect)

        # Draw value
        value_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)

        # Special handling for UUID card - use smaller font and truncate if needed
        if title == "MACHINE UUID":
            value_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
            # Truncate UUID for display in card
            display_value = f"{value}{suffix}"
            if len(display_value) > 15:  # Adjust based on card width
                display_value = display_value[:12] + "..."
            value_text = value_font.render(display_value, True, WHITE)
        else:
            value_text = value_font.render(f"{value}{suffix}", True, WHITE)

        value_rect = value_text.get_rect(x=x + int(15 * self.scale_x), y=y + int(40 * self.scale_y))
        self.screen.blit(value_text, value_rect)

    def draw_uuid_info_section(self, start_y):
        """
        Draw detailed UUID information section for voucher system.

        Args:
            start_y: Y position to start drawing the UUID section

        Returns:
            int: Height of the UUID section
        """
        screen_width = self._layout['screen_width']
        section_height = int(120 * self.scale_y)

        # Get machine UUID
        machine_uuid = self.get_machine_uuid()

        # Section background
        section_rect = pygame.Rect(int(20 * self.scale_x), start_y,
                                 screen_width - int(40 * self.scale_x), section_height)
        pygame.draw.rect(self.screen, (40, 45, 60), section_rect, border_radius=8)
        pygame.draw.rect(self.screen, (100, 120, 150), section_rect, 2, border_radius=8)

        # Title
        title_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        title_text = title_font.render("MACHINE UUID - VOUCHER SYSTEM", True, (255, 255, 255))
        title_rect = title_text.get_rect(x=section_rect.x + int(20 * self.scale_x),
                                       y=start_y + int(10 * self.scale_y))
        self.screen.blit(title_text, title_rect)

        # UUID display
        uuid_y = start_y + int(35 * self.scale_y)
        uuid_font = self.get_font("Courier New", self.scaled_font_size(14), bold=True)

        if machine_uuid:
            # Display full UUID
            uuid_text = uuid_font.render(f"UUID: {machine_uuid}", True, (100, 255, 100))
            uuid_rect = uuid_text.get_rect(x=section_rect.x + int(20 * self.scale_x), y=uuid_y)
            self.screen.blit(uuid_text, uuid_rect)

            # Instructions
            instruction_y = uuid_y + int(25 * self.scale_y)
            instruction_font = self.get_font("Arial", self.scaled_font_size(12))
            instruction_text = "Use this UUID to generate vouchers for this computer. Copy this UUID and provide it to the voucher generator."

            # Word wrap the instruction text
            words = instruction_text.split()
            lines = []
            current_line = []
            max_width = section_rect.width - int(40 * self.scale_x)

            for word in words:
                test_line = ' '.join(current_line + [word])
                test_surface = instruction_font.render(test_line, True, (200, 200, 200))
                if test_surface.get_width() <= max_width:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                    else:
                        lines.append(word)

            if current_line:
                lines.append(' '.join(current_line))

            # Draw instruction lines
            for i, line in enumerate(lines):
                line_surface = instruction_font.render(line, True, (200, 200, 200))
                line_rect = line_surface.get_rect(x=section_rect.x + int(20 * self.scale_x),
                                                y=instruction_y + i * int(15 * self.scale_y))
                self.screen.blit(line_surface, line_rect)

        else:
            # UUID not available
            error_text = uuid_font.render("UUID: Not Available", True, (255, 100, 100))
            error_rect = error_text.get_rect(x=section_rect.x + int(20 * self.scale_x), y=uuid_y)
            self.screen.blit(error_text, error_rect)

            # Error message
            error_msg_y = uuid_y + int(25 * self.scale_y)
            error_msg_font = self.get_font("Arial", self.scaled_font_size(12))
            error_msg = "Unable to retrieve machine UUID. Voucher system may not work properly."
            error_surface = error_msg_font.render(error_msg, True, (255, 150, 150))
            error_msg_rect = error_surface.get_rect(x=section_rect.x + int(20 * self.scale_x), y=error_msg_y)
            self.screen.blit(error_surface, error_msg_rect)

        return section_height

    def _calculate_actual_daily_games(self):
        """
        FIXED: Get accurate daily games count with proper threading and filtering.
        This ensures consistency and accuracy across all displays.

        Returns:
            int: Number of games played today (from database)
        """
        try:
            # FIXED: Import datetime at the top to avoid reference errors
            from datetime import datetime

            # FIXED: First try to get from weekly_stats to avoid threading issues
            today_date = datetime.now().strftime('%Y-%m-%d')

            if hasattr(self, 'weekly_stats') and self.weekly_stats:
                day_stats = next((d for d in self.weekly_stats if d.get('date') == today_date), None)
                if day_stats:
                    daily_count = day_stats.get('games_played', 0)
                    print(f"DEBUG: Daily games from weekly_stats for {today_date}: {daily_count}")
                    return daily_count

            # FIXED: Fallback to database with proper threading handling
            if STATS_DB_AVAILABLE:
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()
                    daily_count = stats_db.get_daily_games_played(today_date)
                    print(f"DEBUG: Database daily games count for {today_date}: {daily_count}")
                    return daily_count
                except Exception as db_error:
                    print(f"DEBUG: Database access failed (threading issue): {db_error}")
                    # Continue to fallback calculation

            # FIXED: Fallback: Calculate from loaded game history with proper filtering
            if not hasattr(self, 'game_history') or not self.game_history:
                return getattr(self, 'daily_games', 0)

            # Count valid games from today in the game history
            daily_count = 0
            for game in self.game_history:
                game_date_str = game.get('date_time', '')
                username = game.get('username', '')
                total_calls = game.get('total_calls', 0)
                status = game.get('status', '')

                if isinstance(game_date_str, str) and game_date_str:
                    try:
                        # Extract date part from datetime string
                        game_date = game_date_str.split(' ')[0]  # Get YYYY-MM-DD part
                        if (game_date == today_date and
                            'Game Reset' not in username and
                            'Demo' not in username and
                            total_calls > 0 and
                            'cancelled' not in status.lower() and
                            'aborted' not in status.lower()):
                            daily_count += 1
                    except Exception:
                        continue

            print(f"DEBUG: Fallback calculated daily games: {daily_count} from {len(self.game_history)} total games")
            return daily_count

        except Exception as e:
            print(f"Error getting actual daily games: {e}")
            # Final fallback to stored daily_games value
            return getattr(self, 'daily_games', 0)

    def _get_centralized_daily_earnings(self, date_str=None):
        """
        CENTRALIZED: Get daily earnings using unified data source and filtering.
        This method ensures ALL sections use identical calculation logic.

        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today

        Returns:
            float: Daily earnings for the specified date
        """
        if date_str is None:
            date_str = datetime.now().strftime('%Y-%m-%d')

        try:
            # Use centralized data provider for consistency
            return self._get_centralized_stats_provider().get_daily_earnings(date_str)
        except Exception as e:
            print(f"Error getting centralized daily earnings: {e}")
            return 0.0

    def _get_centralized_stats_provider(self):
        """
        Get or create the centralized stats data provider.
        This ensures all sections use the same data source and filtering logic.
        """
        if not hasattr(self, '_centralized_stats_provider'):
            self._centralized_stats_provider = CentralizedStatsProvider()
        return self._centralized_stats_provider

    def _get_centralized_total_earnings(self):
        """
        Get total earnings using centralized calculation.

        Returns:
            float: Total earnings across all valid games
        """
        try:
            if STATS_DB_AVAILABLE:
                # Use stats_integration for thread-safe database access
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()

                    # Use existing thread-safe method
                    total = stats_db.get_total_earnings()
                    print(f"CENTRALIZED: Total earnings: {total:.2f} ETB")
                    return total

                except Exception as db_error:
                    print(f"Database access error: {db_error}")
                    return 0.0
        except Exception as e:
            print(f"Error getting centralized total earnings: {e}")
            return 0.0

    def validate_data_consistency(self):
        """
        Validate that all sections show consistent data.
        This method checks for discrepancies between different data sources.

        Returns:
            dict: Validation results with any inconsistencies found
        """
        validation_results = {
            'consistent': True,
            'issues': [],
            'summary': {}
        }

        try:
            from datetime import datetime
            today_date = datetime.now().strftime('%Y-%m-%d')

            # Get data from centralized provider
            centralized_provider = self._get_centralized_stats_provider()

            # Get daily earnings from different sources
            centralized_daily_earnings = centralized_provider.get_daily_earnings(today_date)
            centralized_daily_games = centralized_provider._get_daily_games_played(today_date)
            centralized_total_earnings = self._get_centralized_total_earnings()

            # Get weekly stats
            weekly_stats = centralized_provider.get_weekly_stats()
            today_weekly_stats = next((day for day in weekly_stats if day.get('date') == today_date), None)

            # Store summary
            validation_results['summary'] = {
                'centralized_daily_earnings': centralized_daily_earnings,
                'centralized_daily_games': centralized_daily_games,
                'centralized_total_earnings': centralized_total_earnings,
                'today_date': today_date
            }

            # Check consistency between daily and weekly data
            if today_weekly_stats:
                weekly_daily_earnings = today_weekly_stats.get('earnings', 0)
                weekly_daily_games = today_weekly_stats.get('games_played', 0)

                validation_results['summary']['weekly_daily_earnings'] = weekly_daily_earnings
                validation_results['summary']['weekly_daily_games'] = weekly_daily_games

                # Check earnings consistency
                if abs(centralized_daily_earnings - weekly_daily_earnings) > 0.01:
                    validation_results['consistent'] = False
                    validation_results['issues'].append(
                        f"Daily earnings mismatch: Centralized={centralized_daily_earnings:.2f}, Weekly={weekly_daily_earnings:.2f}"
                    )

                # Check games consistency
                if centralized_daily_games != weekly_daily_games:
                    validation_results['consistent'] = False
                    validation_results['issues'].append(
                        f"Daily games mismatch: Centralized={centralized_daily_games}, Weekly={weekly_daily_games}"
                    )
            else:
                validation_results['issues'].append(f"No weekly stats found for today ({today_date})")

            # Check if stored values match centralized values
            if hasattr(self, 'daily_earnings'):
                if abs(self.daily_earnings - centralized_daily_earnings) > 0.01:
                    validation_results['consistent'] = False
                    validation_results['issues'].append(
                        f"Stored daily earnings mismatch: Stored={self.daily_earnings:.2f}, Centralized={centralized_daily_earnings:.2f}"
                    )

            if hasattr(self, 'daily_games'):
                if self.daily_games != centralized_daily_games:
                    validation_results['consistent'] = False
                    validation_results['issues'].append(
                        f"Stored daily games mismatch: Stored={self.daily_games}, Centralized={centralized_daily_games}"
                    )

            if hasattr(self, 'total_earnings'):
                if abs(self.total_earnings - centralized_total_earnings) > 0.01:
                    validation_results['consistent'] = False
                    validation_results['issues'].append(
                        f"Stored total earnings mismatch: Stored={self.total_earnings:.2f}, Centralized={centralized_total_earnings:.2f}"
                    )

            # Log validation results
            if validation_results['consistent']:
                print("VALIDATION: All data sources are consistent ✓")
            else:
                print("VALIDATION: Data inconsistencies found ✗")
                for issue in validation_results['issues']:
                    print(f"  - {issue}")

            return validation_results

        except Exception as e:
            print(f"Error during data validation: {e}")
            validation_results['consistent'] = False
            validation_results['issues'].append(f"Validation error: {str(e)}")
            return validation_results

    def _calculate_actual_daily_earnings(self, date_str=None):
        """
        DEPRECATED: Use _get_centralized_daily_earnings instead.
        Kept for backward compatibility during transition.
        """
        print("WARNING: Using deprecated _calculate_actual_daily_earnings. Use _get_centralized_daily_earnings instead.")
        return self._get_centralized_daily_earnings(date_str)

    def sync_stats_data(self):
        """
        FIXED: Synchronize all stats data with proper threading handling.
        This method ensures that daily games, earnings, and other stats are accurate.
        """
        try:
            from datetime import datetime
            today_date = datetime.now().strftime('%Y-%m-%d')

            if STATS_DB_AVAILABLE:
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()

                    # FIXED: Try database sync, but handle threading issues gracefully
                    try:
                        stats_db.sync_stats()
                        print("SYNC: Database sync completed")
                    except Exception as sync_error:
                        print(f"SYNC: Database sync failed (threading issue): {sync_error}")
                        # Continue with data retrieval even if sync fails

                    # PERFORMANCE: Return immediately with cached data (no background sync)
                    print("PERFORMANCE: Using cached data for immediate UI responsiveness")

                    # Use existing cached data immediately for UI responsiveness
                    self.daily_earnings = getattr(self, 'daily_earnings', 0.0)
                    self.daily_games = getattr(self, 'daily_games', 0)
                    self.total_earnings = getattr(self, 'total_earnings', 0.0)
                    self.wallet_balance = getattr(self, 'wallet_balance', 0.0)

                    # PERFORMANCE: Skip background sync to prevent threading issues
                    print("PERFORMANCE: Background sync disabled to prevent SQLite threading conflicts")

                    # Use existing weekly stats if available
                    if not hasattr(self, 'weekly_stats') or not self.weekly_stats:
                        # Generate minimal fallback data
                        from datetime import datetime, timedelta
                        today = datetime.now()
                        self.weekly_stats = []
                        for i in range(7):
                            day = today - timedelta(days=6-i)
                            self.weekly_stats.append({
                                'date': day.strftime('%Y-%m-%d'),
                                'games_played': 0,
                                'earnings': 0.0
                            })

                    # Verify consistency between daily and weekly data
                    if self.weekly_stats:
                        today_stats = next((day for day in self.weekly_stats if day.get('date') == today_date), None)
                        if today_stats:
                            weekly_daily_earnings = today_stats.get('earnings', 0)
                            weekly_daily_games = today_stats.get('games_played', 0)

                            # Verify consistency
                            if abs(self.daily_earnings - weekly_daily_earnings) > 0.01:
                                print(f"CENTRALIZED: WARNING - Daily earnings mismatch: {self.daily_earnings:.2f} vs {weekly_daily_earnings:.2f}")
                            if self.daily_games != weekly_daily_games:
                                print(f"CENTRALIZED: WARNING - Daily games mismatch: {self.daily_games} vs {weekly_daily_games}")

                            # Use weekly stats values for absolute consistency
                            self.daily_earnings = weekly_daily_earnings
                            self.daily_games = weekly_daily_games

                    print(f"PERFORMANCE: Sync complete - Games: {self.daily_games}, Earnings: {self.daily_earnings:.2f}, Total: {self.total_earnings:.2f}")
                    print("PERFORMANCE: All sections will now show consistent data")

                    # PERFORMANCE: Skip validation to prevent blocking UI
                    print("PERFORMANCE: Data validation skipped for better performance")

                    return True

                except Exception as db_error:
                    print(f"SYNC: Database access failed: {db_error}")
                    return False
            else:
                print("SYNC: Database not available, cannot sync stats")
                return False

        except Exception as e:
            print(f"Error syncing stats data: {e}")
            return False

    def _sync_stats_background(self):
        """PERFORMANCE: Background thread for stats synchronization without blocking UI."""
        try:
            from datetime import datetime
            today_date = datetime.now().strftime('%Y-%m-%d')

            print("BACKGROUND SYNC: Starting database synchronization...")

            # Use timeout to prevent hanging
            import queue

            result_queue = queue.Queue()

            def sync_data_async():
                try:
                    from stats_integration import get_stats_db_manager
                    stats_db = get_stats_db_manager()

                    # Get fresh data with timeout protection
                    daily_earnings = stats_db.get_daily_earnings(today_date)
                    daily_games = stats_db.get_daily_games_played(today_date)
                    total_earnings = stats_db.get_total_earnings()

                    # Get weekly stats
                    weekly_stats = stats_db.get_weekly_stats()

                    result_queue.put(('success', {
                        'daily_earnings': daily_earnings,
                        'daily_games': daily_games,
                        'total_earnings': total_earnings,
                        'weekly_stats': weekly_stats
                    }))
                except Exception as e:
                    result_queue.put(('error', str(e)))

            # Start async operation with timeout
            thread = threading.Thread(target=sync_data_async, daemon=True)
            thread.start()

            try:
                result_type, result_value = result_queue.get(timeout=3.0)  # 3 second timeout
                if result_type == 'success':
                    # Update stats with fresh data
                    data = result_value
                    self.daily_earnings = data['daily_earnings']
                    self.daily_games = data['daily_games']
                    self.total_earnings = data['total_earnings']
                    if data['weekly_stats']:
                        self.weekly_stats = data['weekly_stats']

                    print(f"BACKGROUND SYNC: Complete - Games: {self.daily_games}, Earnings: {self.daily_earnings:.2f}")
                else:
                    print(f"BACKGROUND SYNC: Database error: {result_value}")
            except queue.Empty:
                print("BACKGROUND SYNC: Timeout - using cached data")

        except Exception as e:
            print(f"BACKGROUND SYNC: Error: {e}")

    def draw_search_bar(self, start_y):
        """
        Draw the search bar above the game history table.

        Args:
            start_y: Y position to start drawing the search bar

        Returns:
            int: Height of the search bar area
        """
        screen_width = self._layout['screen_width']

        # Search bar dimensions (compact)
        search_bar_height = int(45 * self.scale_y)
        search_input_width = int(350 * self.scale_x)
        search_input_height = int(30 * self.scale_y)

        # Calculate positions
        search_x = int(20 * self.scale_x)
        search_y = start_y + int(10 * self.scale_y)

        # Store search input rectangle for click detection
        self.search_input_rect = pygame.Rect(search_x, search_y, search_input_width, search_input_height)

        # Draw search input background
        input_bg_color = (255, 255, 255) if self.search_active else (240, 240, 240)
        border_color = (100, 150, 255) if self.search_active else (180, 180, 180)
        border_width = 2 if self.search_active else 1

        pygame.draw.rect(self.screen, input_bg_color, self.search_input_rect, border_radius=5)
        pygame.draw.rect(self.screen, border_color, self.search_input_rect, border_width, border_radius=5)

        # Draw search icon
        icon_size = int(20 * self.scale_y)
        icon_x = search_x + int(10 * self.scale_x)
        icon_y = search_y + (search_input_height - icon_size) // 2

        # Simple search icon (magnifying glass)
        icon_color = (120, 120, 120)
        pygame.draw.circle(self.screen, icon_color,
                         (icon_x + icon_size//3, icon_y + icon_size//3),
                         icon_size//4, 2)
        pygame.draw.line(self.screen, icon_color,
                        (icon_x + icon_size//2, icon_y + icon_size//2),
                        (icon_x + icon_size*3//4, icon_y + icon_size*3//4), 2)

        # Draw search text or placeholder
        text_x = icon_x + icon_size + int(10 * self.scale_x)
        text_y = search_y + search_input_height // 2

        search_font = self.get_font("Arial", self.scaled_font_size(12))

        if self.search_query:
            # Draw search query text
            search_text = search_font.render(self.search_query, True, (0, 0, 0))
            text_rect = search_text.get_rect(left=text_x, centery=text_y)
            self.screen.blit(search_text, text_rect)

            # Draw cursor if search is active
            if self.search_active:
                cursor_x = text_rect.right + 2
                cursor_y1 = text_rect.top + 2
                cursor_y2 = text_rect.bottom - 2
                pygame.draw.line(self.screen, (0, 0, 0), (cursor_x, cursor_y1), (cursor_x, cursor_y2), 1)
        else:
            # Draw placeholder text
            placeholder_text = search_font.render("Search games...", True, (150, 150, 150))
            placeholder_rect = placeholder_text.get_rect(left=text_x, centery=text_y)
            self.screen.blit(placeholder_text, placeholder_rect)

        # Draw clear button if there's search text
        if self.search_query:
            clear_size = int(20 * self.scale_y)
            clear_x = search_x + search_input_width - clear_size - int(10 * self.scale_x)
            clear_y = search_y + (search_input_height - clear_size) // 2

            self.search_clear_rect = pygame.Rect(clear_x, clear_y, clear_size, clear_size)

            # Draw clear button background (subtle circle)
            pygame.draw.circle(self.screen, (220, 220, 220),
                             (clear_x + clear_size//2, clear_y + clear_size//2),
                             clear_size//2)

            # Draw X icon
            x_color = (100, 100, 100)
            margin = clear_size // 4
            pygame.draw.line(self.screen, x_color,
                           (clear_x + margin, clear_y + margin),
                           (clear_x + clear_size - margin, clear_y + clear_size - margin), 2)
            pygame.draw.line(self.screen, x_color,
                           (clear_x + clear_size - margin, clear_y + margin),
                           (clear_x + margin, clear_y + clear_size - margin), 2)
        else:
            self.search_clear_rect = None

        # Draw search results counter
        if self.search_query.strip():
            result_count = len(self.search_results) if hasattr(self, 'search_results') else 0
            counter_x = search_x + search_input_width + int(20 * self.scale_x)
            counter_y = text_y

            counter_font = self.get_font("Arial", self.scaled_font_size(11))
            if result_count == 0:
                counter_text = counter_font.render("No results found", True, (200, 100, 100))
            elif result_count == 1:
                counter_text = counter_font.render("1 result found", True, (100, 200, 100))
            else:
                counter_text = counter_font.render(f"{result_count} results found", True, (100, 200, 100))

            counter_rect = counter_text.get_rect(left=counter_x, centery=counter_y)
            self.screen.blit(counter_text, counter_rect)

        # Draw credit history toggle button
        toggle_button_width = int(150 * self.scale_x)
        toggle_button_height = int(30 * self.scale_y)
        toggle_x = screen_width - toggle_button_width - int(20 * self.scale_x)
        toggle_y = search_y + (search_input_height - toggle_button_height) // 2

        # Store hit area for toggle button
        toggle_rect = pygame.Rect(toggle_x, toggle_y, toggle_button_width, toggle_button_height)
        self.hit_areas["credit_history_toggle"] = toggle_rect

        # Draw toggle button background
        toggle_bg_color = (100, 150, 200) if self.credit_history_visible else (80, 80, 80)
        pygame.draw.rect(self.screen, toggle_bg_color, toggle_rect, border_radius=5)

        # Draw toggle button text
        toggle_font = self.get_font("Arial", self.scaled_font_size(11), bold=True)
        toggle_text = "Hide Credit History" if self.credit_history_visible else "Show Credit History"
        text_surface = toggle_font.render(toggle_text, True, WHITE)
        text_rect = text_surface.get_rect(center=toggle_rect.center)
        self.screen.blit(text_surface, text_rect)

        return search_bar_height

    def _group_game_history_by_date(self, game_history):
        """
        Group game history by date and add session IDs and date separators.

        Args:
            game_history: List of game history records

        Returns:
            List of processed records including date separators and session IDs
        """
        if not game_history:
            return []

        from datetime import datetime
        from collections import defaultdict

        # Group games by date
        games_by_date = defaultdict(list)

        for game in game_history:
            try:
                # Extract date from date_time field
                date_time_str = game.get('date_time', '')
                if isinstance(date_time_str, str) and date_time_str:
                    # Parse the date
                    dt = datetime.strptime(date_time_str, '%Y-%m-%d %H:%M:%S')
                    date_key = dt.strftime('%Y-%m-%d')
                    day_name = dt.strftime('%A')  # Get day of week
                    games_by_date[date_key].append({
                        **game,
                        'parsed_date': dt,
                        'day_name': day_name
                    })
                else:
                    # Handle games without proper date
                    games_by_date['unknown'].append({
                        **game,
                        'parsed_date': None,
                        'day_name': 'Unknown'
                    })
            except Exception as e:
                print(f"Error parsing date for game: {e}")
                games_by_date['unknown'].append({
                    **game,
                    'parsed_date': None,
                    'day_name': 'Unknown'
                })

        # Sort dates chronologically (most recent first)
        sorted_dates = sorted(games_by_date.keys(), reverse=True)
        if 'unknown' in sorted_dates:
            # Move unknown to the end
            sorted_dates.remove('unknown')
            sorted_dates.append('unknown')

        # Build the final list with date separators and session IDs
        processed_history = []

        for date_key in sorted_dates:
            games_for_date = games_by_date[date_key]

            # FIXED: Sort games within the date by time (latest first - Session-3, Session-2, Session-1)
            games_for_date.sort(key=lambda x: x.get('parsed_date') or datetime.min, reverse=True)

            # Add date separator
            if games_for_date:
                day_name = games_for_date[0]['day_name']
                # Format: *********Day:[in Red Bold color] *************
                separator = {
                    'is_date_separator': True,
                    'date_key': date_key,
                    'day_name': day_name,
                    'display_text': f"*********{day_name}:*************",
                    'formatted_date': date_key  # Store the actual date for reference
                }
                processed_history.append(separator)

                # Add games with session IDs (reversed numbering to match latest-first order)
                total_games = len(games_for_date)
                for i, game in enumerate(games_for_date):
                    game_copy = game.copy()
                    # Reverse the session numbering: latest game gets highest number
                    session_number = total_games - i
                    game_copy['session_id'] = f"Session-{session_number}"
                    game_copy['is_date_separator'] = False
                    processed_history.append(game_copy)

        return processed_history

    def _draw_date_separator(self, x, y, width, height, text, font):
        """
        Draw a professional date separator row with enhanced styling and animations.

        Args:
            x, y: Position of the separator
            width, height: Dimensions of the separator
            text: Text to display (e.g., "*********Wednesday:*************")
            font: Font to use for the text
        """
        # Get current time for animations
        current_time = pygame.time.get_ticks()

        # Create professional gradient background
        separator_rect = pygame.Rect(x, y, width, height)

        # Enhanced gradient background (dark blue to darker blue with subtle animation)
        gradient_steps = 15
        animation_offset = math.sin(current_time * 0.001) * 0.1

        for i in range(gradient_steps):
            step_height = height // gradient_steps
            step_y = y + (i * step_height)

            # Professional gradient colors with animation
            base_intensity = 0.8 + animation_offset
            start_color = (int(20 * base_intensity), int(30 * base_intensity), int(50 * base_intensity))
            end_color = (int(35 * base_intensity), int(45 * base_intensity), int(70 * base_intensity))

            # Interpolate colors
            ratio = i / gradient_steps
            r = int(start_color[0] + (end_color[0] - start_color[0]) * ratio)
            g = int(start_color[1] + (end_color[1] - start_color[1]) * ratio)
            b = int(start_color[2] + (end_color[2] - start_color[2]) * ratio)

            step_rect = pygame.Rect(x, step_y, width, step_height + 1)
            pygame.draw.rect(self.screen, (r, g, b), step_rect)

        # Add professional border with glow effect
        border_intensity = abs(math.sin(current_time * 0.003)) * 0.4 + 0.6
        border_color = (int(100 * border_intensity), int(150 * border_intensity), int(255 * border_intensity))

        # Top and bottom accent lines
        pygame.draw.line(self.screen, border_color, (x, y), (x + width, y), 2)
        pygame.draw.line(self.screen, border_color, (x, y + height - 1), (x + width, y + height - 1), 2)

        # Side accent lines with gradient
        for i in range(height):
            alpha = 1 - (abs(i - height//2) / (height//2)) * 0.5
            side_color = (int(border_color[0] * alpha), int(border_color[1] * alpha), int(border_color[2] * alpha))
            pygame.draw.line(self.screen, side_color, (x, y + i), (x + 2, y + i), 1)
            pygame.draw.line(self.screen, side_color, (x + width - 3, y + i), (x + width - 1, y + i), 1)

        # Parse and render text with professional styling
        if ":" in text:
            parts = text.split(":")
            if len(parts) >= 2:
                day_part = parts[0].replace("*", "").strip()

                center_x = x + width // 2
                center_y = y + height // 2

                # Draw decorative elements (performance optimized)
                # Only draw decorative elements on certain frames to improve performance
                if not hasattr(self, '_frame_count') or self._frame_count % 4 != 0:
                    self._draw_decorative_elements(center_x, center_y, current_time, width)

                # Enhanced day name with multiple effects
                day_font = pygame.font.SysFont("Arial", int(font.get_height() * 1.3), bold=True)

                # Create text with shadow and glow effects
                self._draw_text_with_effects(day_part, day_font, center_x, center_y, current_time)
        else:
            # Fallback with enhanced styling
            center_x = x + width // 2
            center_y = y + height // 2
            self._draw_text_with_effects(text.replace("*", ""), font, center_x, center_y, current_time)

    def _draw_decorative_elements(self, center_x, center_y, current_time, width):
        """Draw professional decorative elements around the date separator."""
        # Calculate responsive line length based on available width
        max_line_length = min(100, width // 8)
        line_length = max(40, max_line_length)
        line_offset = min(150, width // 6)

        # Animated pulse effect for decorative lines
        pulse = abs(math.sin(current_time * 0.004)) * 0.3 + 0.7
        line_color = (int(120 * pulse), int(180 * pulse), int(255 * pulse))

        # Professional decorative lines with gradient effect
        for i in range(3):
            offset_y = (i - 1) * 2
            alpha = 1 - abs(i - 1) * 0.4
            current_color = (int(line_color[0] * alpha), int(line_color[1] * alpha), int(line_color[2] * alpha))

            # Left decorative line
            left_start = (center_x - line_offset, center_y + offset_y)
            left_end = (center_x - line_offset + line_length, center_y + offset_y)
            pygame.draw.line(self.screen, current_color, left_start, left_end, 2 if i == 1 else 1)

            # Right decorative line
            right_start = (center_x + line_offset - line_length, center_y + offset_y)
            right_end = (center_x + line_offset, center_y + offset_y)
            pygame.draw.line(self.screen, current_color, right_start, right_end, 2 if i == 1 else 1)

        # Professional corner accents
        accent_size = 8
        accent_color = (255, 220, 100)

        # Left accent
        left_accent_x = center_x - line_offset - 20
        self._draw_corner_accent(left_accent_x, center_y, accent_size, accent_color)

        # Right accent
        right_accent_x = center_x + line_offset + 20
        self._draw_corner_accent(right_accent_x, center_y, accent_size, accent_color)

    def _draw_text_with_effects(self, text, font, center_x, center_y, current_time):
        """Draw text with professional shadow and glow effects."""
        # Animated glow intensity
        glow_intensity = abs(math.sin(current_time * 0.002)) * 0.3 + 0.7

        # Multiple shadow layers for depth
        shadow_offsets = [(3, 3), (2, 2), (1, 1)]
        shadow_colors = [(0, 0, 0), (20, 20, 20), (40, 40, 40)]

        for offset, shadow_color in zip(shadow_offsets, shadow_colors):
            shadow_text = font.render(text, True, shadow_color)
            shadow_rect = shadow_text.get_rect(center=(center_x + offset[0], center_y + offset[1]))
            self.screen.blit(shadow_text, shadow_rect)

        # Main text with gradient effect
        main_color = (int(255 * glow_intensity), int(220 * glow_intensity), int(100 * glow_intensity))
        main_text = font.render(text, True, main_color)
        main_rect = main_text.get_rect(center=(center_x, center_y))
        self.screen.blit(main_text, main_rect)

        # Subtle highlight on top
        highlight_color = (255, 255, 200)
        highlight_text = font.render(text, True, highlight_color)
        highlight_rect = highlight_text.get_rect(center=(center_x, center_y - 1))

        # Create a surface for the highlight with alpha
        highlight_surface = pygame.Surface(highlight_text.get_size(), pygame.SRCALPHA)
        highlight_surface.blit(highlight_text, (0, 0))
        highlight_surface.set_alpha(int(100 * glow_intensity))
        self.screen.blit(highlight_surface, highlight_rect)

    def _draw_corner_accent(self, x, y, size, color):
        """Draw a professional corner accent."""
        # Create a small diamond-like accent
        points = [
            (x, y - size//2),      # Top
            (x + size//2, y),      # Right
            (x, y + size//2),      # Bottom
            (x - size//2, y)       # Left
        ]
        pygame.draw.polygon(self.screen, color, points)

        # Add a smaller inner accent
        inner_size = size // 2
        inner_points = [
            (x, y - inner_size//2),
            (x + inner_size//2, y),
            (x, y + inner_size//2),
            (x - inner_size//2, y)
        ]
        inner_color = tuple(min(255, c + 50) for c in color)
        pygame.draw.polygon(self.screen, inner_color, inner_points)

    def handle_table_mouse_motion(self, mouse_pos):
        """
        Handle mouse motion over the game history table for hover effects.

        Args:
            mouse_pos: Current mouse position (x, y)
        """
        old_hovered = self.hovered_row_index
        self.hovered_row_index = -1

        # Check if mouse is over any table row
        for i, row_rect in enumerate(self.table_rows):
            if row_rect.collidepoint(mouse_pos):
                self.hovered_row_index = i
                break

        # Update cursor based on hover state
        if self.hovered_row_index != -1:
            # Set hand cursor when hovering over a row
            pygame.mouse.set_cursor(pygame.SYSTEM_CURSOR_HAND)
        elif old_hovered != -1:
            # Reset to default cursor when leaving a row
            pygame.mouse.set_cursor(pygame.SYSTEM_CURSOR_ARROW)

        # Return True if hover state changed (to trigger redraw)
        return old_hovered != self.hovered_row_index

    def handle_table_mouse_click(self, mouse_pos):
        """
        Handle mouse clicks on the game history table for selection.

        Args:
            mouse_pos: Mouse click position (x, y)

        Returns:
            bool: True if a row was clicked, False otherwise
        """
        current_time = pygame.time.get_ticks()

        # Check if mouse clicked on any table row
        for i, row_rect in enumerate(self.table_rows):
            if row_rect.collidepoint(mouse_pos):
                # Check for double-click (within 500ms)
                is_double_click = (current_time - self.last_click_time < 500 and
                                 self.selected_row_index == i)

                if is_double_click:
                    # Handle double-click - could show detailed view
                    self.handle_row_double_click(i)
                else:
                    # Single click - select the row
                    self.selected_row_index = i

                    # Show selection feedback with row information
                    try:
                        processed_history = self._group_game_history_by_date(self.game_history)
                        if 0 <= i < len(processed_history):
                            record = processed_history[i]
                            if record.get('is_date_separator', False):
                                day_name = record.get('day_name', 'Unknown')
                                self.show_message(f"Selected date section: {day_name}", "info")
                            else:
                                session_id = record.get('session_id', 'N/A')
                                player_name = record.get('username', 'Unknown')
                                self.show_message(f"Selected: {session_id} - {player_name}", "info")
                        else:
                            self.show_message(f"Selected row {i + 1}", "info")
                    except Exception as e:
                        print(f"Error showing selection info: {e}")
                        self.show_message(f"Selected row {i + 1}", "info")

                self.last_click_time = current_time
                return True

        # Click outside table - deselect
        if self.selected_row_index != -1:
            self.selected_row_index = -1
            return True

        return False

    def handle_row_double_click(self, row_index):
        """
        Handle double-click on a table row.

        Args:
            row_index: Index of the double-clicked row
        """
        try:
            # Get the processed history to find the actual game data
            if hasattr(self, 'game_history') and self.game_history:
                processed_history = self._group_game_history_by_date(self.game_history)

                if 0 <= row_index < len(processed_history):
                    record = processed_history[row_index]

                    if record.get('is_date_separator', False):
                        # Double-clicked on date separator
                        day_name = record.get('day_name', 'Unknown')
                        self.show_message(f"Date section: {day_name}", "info")
                    else:
                        # Double-clicked on game record
                        session_id = record.get('session_id', 'N/A')
                        player_name = record.get('username', 'Unknown')
                        status = record.get('status', 'Unknown')
                        self.show_message(f"Game Details: {session_id} - {player_name} ({status})", "info")
        except Exception as e:
            print(f"Error handling row double-click: {e}")
            self.show_message("Error displaying game details", "error")

    def handle_credit_table_mouse_click(self, mouse_pos):
        """
        Handle mouse clicks on the credit history table for selection.

        Args:
            mouse_pos: Mouse click position (x, y)

        Returns:
            bool: True if a row was clicked, False otherwise
        """
        current_time = pygame.time.get_ticks()

        # Check if mouse clicked on any credit table row
        for i, row_rect in enumerate(self.credit_table_rows):
            if row_rect.collidepoint(mouse_pos):
                # Check for double-click (within 500ms)
                is_double_click = (current_time - self.last_click_time < 500 and
                                 self.credit_selected_row_index == i)

                if is_double_click:
                    # Handle double-click - show detailed credit information
                    self.handle_credit_row_double_click(i)
                else:
                    # Single click - select the row
                    self.credit_selected_row_index = i

                    # Show selection feedback with credit information
                    try:
                        processed_credit_history = self._group_credit_history_by_date(self.credit_history)
                        if 0 <= i < len(processed_credit_history):
                            record = processed_credit_history[i]
                            if record.get('is_date_separator', False):
                                day_name = record.get('day_name', 'Unknown')
                                self.show_message(f"Selected credit date section: {day_name}", "info")
                            else:
                                transaction_id = record.get('transaction_id', 'N/A')
                                amount = record.get('credit_amount', 0)
                                status = record.get('status', 'Unknown')
                                remaining_date = record.get('remaining_date', 'N/A')
                                self.show_message(f"Selected: {transaction_id} - {amount} ETB ({status}) - {remaining_date}", "info")
                        else:
                            self.show_message(f"Selected credit row {i + 1}", "info")
                    except Exception as e:
                        print(f"Error showing credit selection info: {e}")
                        self.show_message(f"Selected credit row {i + 1}", "info")

                self.last_click_time = current_time
                return True

        # Click outside table - deselect
        if self.credit_selected_row_index != -1:
            self.credit_selected_row_index = -1
            return True

        return False

    def handle_credit_row_double_click(self, row_index):
        """Handle double-click on credit history row to show detailed information."""
        try:
            processed_credit_history = self._group_credit_history_by_date(self.credit_history)

            if 0 <= row_index < len(processed_credit_history):
                record = processed_credit_history[row_index]

                if record.get('is_date_separator', False):
                    # Double-clicked on date separator
                    day_name = record.get('day_name', 'Unknown')
                    formatted_date = record.get('formatted_date', 'Unknown')
                    self.show_message(f"Credit date section: {day_name} ({formatted_date})", "info")
                else:
                    # Double-clicked on credit record - show detailed information
                    transaction_id = record.get('transaction_id', 'N/A')
                    amount = record.get('credit_amount', 0)
                    remaining = record.get('remaining_balance', 0)
                    status = record.get('status', 'Unknown')
                    expiry = record.get('expiry_date', 'No Expiry')
                    method = record.get('payment_method', 'Unknown')

                    detail_msg = f"Credit Details: {transaction_id} | Amount: {amount} ETB | Remaining: {remaining} ETB | Status: {status} | Method: {method} | Expiry: {expiry}"
                    self.show_message(detail_msg, "info")
        except Exception as e:
            print(f"Error handling credit row double-click: {e}")
            self.show_message("Error displaying credit details", "error")

    def draw_game_history(self, start_y):
        """Draw game history table

        Returns:
            int: Height of the game history section
        """
        screen_width = self._layout['screen_width']
        screen_height = self._layout['screen_height']

        # FIXED: Calculate proper table dimensions to ensure all content is visible
        available_height = screen_height - start_y - self.notification_height - int(40 * self.scale_y)

        # Use more space for game history table to prevent content cutoff
        max_table_height = int(available_height * 0.7)  # Use 70% of remaining space

        # Responsive table width with minimum margins
        min_margin = int(15 * self.scale_x)
        table_width = screen_width - (min_margin * 2)

        # Proper table height to accommodate content
        min_table_height = int(200 * self.scale_y)  # Increased minimum height
        optimal_table_height = int(300 * self.scale_y)  # Increased optimal height for proper display
        table_height = max(min_table_height, min(max_table_height, optimal_table_height))

        table_x = min_margin
        table_y = start_y

        # Draw table title
        title_font = self.get_font("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Game History", True, WHITE)
        title_rect = title_text.get_rect(x=table_x, y=table_y)
        self.screen.blit(title_text, title_rect)

        # Calculate table position (below title)
        table_y += title_rect.height + int(10 * self.scale_y)
        table_height -= title_rect.height + int(10 * self.scale_y)

        # Draw table background
        table_rect = pygame.Rect(table_x, table_y, table_width, table_height)
        pygame.draw.rect(self.screen, CARD_BG, table_rect, border_radius=5)

        # OPTIMIZED: Responsive column layout with perfect width distribution (totals exactly 1.0)
        columns = [
            {"id": "session_id", "name": "SESSION", "width": 0.08, "align": "center", "min_width": 65},
            {"id": "house", "name": "WINNER PATTERN", "width": 0.15, "align": "center", "min_width": 130},
            {"id": "stake", "name": "STAKE", "width": 0.09, "align": "right", "min_width": 80},
            {"id": "players", "name": "PLAYERS", "width": 0.07, "align": "center", "min_width": 55},
            {"id": "total_calls", "name": "CALLS", "width": 0.06, "align": "center", "min_width": 45},
            {"id": "commission_percent", "name": "COMMISSION %", "width": 0.08, "align": "center", "min_width": 65},
            {"id": "fee", "name": "COMMISSION", "width": 0.11, "align": "right", "min_width": 90},
            {"id": "total_prize", "name": "PRIZE POOL", "width": 0.11, "align": "right", "min_width": 90},
            {"id": "remainder", "name": "REMAINDER", "width": 0.08, "align": "center", "min_width": 70},
            {"id": "date_time", "name": "DATE & TIME", "width": 0.11, "align": "center", "min_width": 85},
            {"id": "status", "name": "STATUS", "width": 0.06, "align": "center", "min_width": 45}
        ]

        # OPTIMIZED: Calculate compact dimensions for better space utilization
        base_row_height = max(22, min(28, int(25 * self.scale_y)))  # Compact row height
        base_header_height = max(25, min(35, int(30 * self.scale_y)))  # Compact header height

        # Ensure minimum readability while maximizing visible rows
        row_height = base_row_height
        header_height = base_header_height

        # Calculate responsive column widths with minimum width enforcement
        responsive_columns = self._calculate_responsive_column_widths(columns, table_width)

        # Draw enhanced table header with gradient effect
        header_rect = pygame.Rect(table_x, table_y, table_width, header_height)

        # Create gradient header background
        header_surface = pygame.Surface((table_width, header_height))
        # Optimized gradient rendering - skip frames for performance
        gradient_step = max(1, header_height // 20)  # Reduce gradient steps
        for i in range(0, header_height, gradient_step):
            alpha = 1 - (i / header_height) * 0.3
            color = (
                int(TABLE_HEADER_BG[0] * alpha),
                int(TABLE_HEADER_BG[1] * alpha),
                int(TABLE_HEADER_BG[2] * alpha)
            )
            pygame.draw.line(header_surface, color, (0, i), (table_width, i))

        self.screen.blit(header_surface, (table_x, table_y))

        # Add header border
        pygame.draw.rect(self.screen, (60, 80, 120), header_rect, 2, border_radius=5)

        # Draw column headers with proper alignment and responsive widths
        header_font = self.get_font("Arial", self.scaled_font_size(11), bold=True)
        x_offset = table_x

        for column in responsive_columns:
            col_width = int(table_width * column["width"])

            # OPTIMIZED: Truncate header text to fit column width
            header_name = self._truncate_text_to_fit(column["name"], header_font, col_width - 16)
            header_text = header_font.render(header_name, True, WHITE)

            # Align header text based on column alignment with consistent padding
            padding = 8
            if column["align"] == "center":
                header_rect = header_text.get_rect(centerx=x_offset + col_width//2, centery=table_y + header_height//2)
            elif column["align"] == "right":
                header_rect = header_text.get_rect(right=x_offset + col_width - padding, centery=table_y + header_height//2)
            else:  # left align
                header_rect = header_text.get_rect(left=x_offset + padding, centery=table_y + header_height//2)

            # Ensure header text doesn't overflow
            if header_rect.right > x_offset + col_width - padding:
                header_rect.right = x_offset + col_width - padding
            if header_rect.left < x_offset + padding:
                header_rect.left = x_offset + padding

            self.screen.blit(header_text, header_rect)

            # Draw column separator
            if x_offset + col_width < table_x + table_width:
                separator_x = x_offset + col_width
                pygame.draw.line(self.screen, (80, 100, 140),
                               (separator_x, table_y + 5),
                               (separator_x, table_y + header_height - 5), 1)

            x_offset += col_width

        # Draw table rows with enhanced formatting and date grouping
        # FLICKERING FIX: Use stable game history data to prevent blinking
        stable_history = self.get_stable_ui_data('game_history', [])

        # FLICKERING FIX: Only update stable data if we have new data and enough time has passed
        if hasattr(self, 'game_history') and self.game_history:
            # ANTI-BLINK: More conservative update - only if data actually changed
            if not stable_history:
                # First time - set stable data
                self.set_stable_ui_data('game_history', self.game_history.copy())
                stable_history = self.game_history
                print("ANTI-BLINK: Initial game history data set")
            elif (len(self.game_history) != len(stable_history) or 
                  self.game_history != stable_history) and self.should_update_section('game_history'):
                # Only update if data actually changed and cooldown passed
                self.set_stable_ui_data('game_history', self.game_history.copy())
                stable_history = self.game_history
                print(f"ANTI-BLINK: Game history updated - new count: {len(self.game_history)}")
                # FINAL FIX: Invalidate display cache when data changes
                self.invalidate_display_cache("game_history_updated")

        # FLICKERING FIX: Always use stable data for rendering
        if not stable_history and hasattr(self, 'game_history') and self.game_history:
            # ANTI-BLINK: Use fallback only once and cache it
            stable_history = self.game_history.copy()
            self.set_stable_ui_data('game_history', stable_history)
            print("ANTI-BLINK: Used fallback game history data and cached it")

        if stable_history and len(stable_history) > 0:
            # FLICKERING FIX: Use stable_history instead of self.game_history for rendering
            filtered_history = []
            for record in stable_history:
                # Validate record has essential fields
                if not record.get('date_time') or not record.get('username'):
                    continue

                # Validate data consistency
                players = record.get('players', 0)
                total_calls = record.get('total_calls', 0)
                stake = record.get('stake', 0)

                # Skip records with impossible data
                if players < 0 or players > 50:  # Reasonable player limits
                    continue
                if total_calls < 0 or total_calls > 75:  # Bingo has max 75 numbers
                    continue
                if stake < 0 or stake > 1000:  # Reasonable stake limits
                    continue

                # Include valid records
                filtered_history.append(record)

            print(f"DEBUG: Displaying {len(filtered_history)} validated game history records")
            if filtered_history:
                print(f"DEBUG: First record: {filtered_history[0].get('username', 'Unknown')} - {filtered_history[0].get('date_time', 'No date')}")
                print(f"DEBUG: Last record: {filtered_history[-1].get('username', 'Unknown')} - {filtered_history[-1].get('date_time', 'No date')}")

            # Group game history by date using filtered data
            processed_history = self._group_game_history_by_date(filtered_history)

            # Clear and rebuild table rows for hit detection
            self.table_rows = []

            row_font = self.get_font("Arial", self.scaled_font_size(10))
            row_font_bold = self.get_font("Arial", self.scaled_font_size(10), bold=True)
            separator_font = self.get_font("Arial", self.scaled_font_size(12), bold=True)

            visible_row_count = 0
            for i, record in enumerate(processed_history):
                # Calculate row position
                row_y = table_y + header_height + visible_row_count * row_height

                # Skip if row is outside visible area
                if row_y + row_height > table_y + table_height:
                    continue

                # Create row rectangle for hit detection
                row_rect = pygame.Rect(table_x, row_y, table_width, row_height)
                self.table_rows.append(row_rect)

                # Check if this is a date separator
                if record.get('is_date_separator', False):
                    # Draw date separator row
                    self._draw_date_separator(table_x, row_y, table_width, row_height,
                                            record['display_text'], separator_font)
                else:
                    # Draw regular game row with highlighting
                    # Determine row background color based on state
                    base_color = TABLE_ROW_BG_1 if visible_row_count % 2 == 0 else TABLE_ROW_BG_2

                    # Apply highlighting effects
                    if visible_row_count == self.selected_row_index:
                        # Selected row - bright blue highlight
                        row_color = (60, 120, 200)  # Bright blue
                        border_color = (100, 150, 255)  # Lighter blue border
                        border_width = 3
                    elif visible_row_count == self.hovered_row_index:
                        # Hovered row - subtle highlight
                        row_color = tuple(min(255, c + 30) for c in base_color)  # Lighten base color
                        border_color = (120, 140, 180)  # Light blue-gray border
                        border_width = 2
                    else:
                        # Normal row
                        row_color = base_color
                        border_color = (70, 80, 100)
                        border_width = 1

                    # Draw row background
                    pygame.draw.rect(self.screen, row_color, row_rect)

                    # Draw row border
                    if border_width > 1:
                        pygame.draw.rect(self.screen, border_color, row_rect, border_width)
                    else:
                        pygame.draw.line(self.screen, border_color,
                                       (table_x, row_y + row_height),
                                       (table_x + table_width, row_y + row_height), border_width)

                    # Draw row data with proper alignment and responsive widths
                    x_offset = table_x

                    for column in responsive_columns:
                        col_width = int(table_width * column["width"])
                        col_id = column["id"]

                        # Get and format value for this column
                        value = self._format_cell_value(record, col_id)

                        # Choose font and color based on column
                        font = row_font_bold if col_id in ["fee", "total_prize", "status"] else row_font
                        text_color = self._get_cell_color(col_id, value, record)

                        # Apply search highlighting if there's an active search
                        if self.search_query.strip():
                            highlighted_value, has_match = self.highlight_search_match(value, self.search_query)

                            # Create cell rectangle for text positioning
                            cell_rect = pygame.Rect(x_offset, row_y, col_width, row_height)

                            # Draw highlighted text
                            self.draw_highlighted_text(self.screen, highlighted_value, font, text_color,
                                                     cell_rect, column["align"])
                        else:
                            # OPTIMIZED: Render text with proper truncation and fitting
                            truncated_value = self._truncate_text_to_fit(value, font, col_width - 20)
                            cell_text = font.render(truncated_value, True, text_color)

                            # Align text based on column alignment with proper padding
                            padding = 8  # Consistent padding for all columns
                            if column["align"] == "center":
                                cell_rect = cell_text.get_rect(centerx=x_offset + col_width//2, centery=row_y + row_height//2)
                            elif column["align"] == "right":
                                cell_rect = cell_text.get_rect(right=x_offset + col_width - padding, centery=row_y + row_height//2)
                            else:  # left align
                                cell_rect = cell_text.get_rect(left=x_offset + padding, centery=row_y + row_height//2)

                            # Ensure text doesn't overflow column boundaries
                            if cell_rect.right > x_offset + col_width - padding:
                                cell_rect.right = x_offset + col_width - padding
                            if cell_rect.left < x_offset + padding:
                                cell_rect.left = x_offset + padding

                            self.screen.blit(cell_text, cell_rect)

                        x_offset += col_width

                visible_row_count += 1

            # Draw pagination controls if needed
            if self.total_history_pages > 1:
                self.draw_pagination_controls(table_x, table_y + table_height + int(10 * self.scale_y), table_width)

        else:
            # Draw "No data" message when no game history is available
            no_data_font = self.get_font("Arial", self.scaled_font_size(16))

            # Check if this is a post-cleanup state
            # FINAL FIX: Cache database check to prevent queries every frame
            cached_cleanup_state = self.get_cached_display_state('post_cleanup_check')
            
            if cached_cleanup_state is not None:
                is_post_cleanup = cached_cleanup_state
            else:
                is_post_cleanup = False
                if STATS_DB_AVAILABLE:
                    try:
                        # Try to get a direct count from the database
                        from stats_db import get_stats_db_manager
                        # Use hybrid DB if available
                        if HYBRID_DB_AVAILABLE:
                            stats_db = get_hybrid_db_integration()
                        else:
                            stats_db = get_stats_db_manager()
                        with stats_db.get_connection_context() as conn:
                            cursor = conn.cursor()
                            cursor.execute("SELECT COUNT(*) FROM game_history")
                            count = cursor.fetchone()[0]
                            is_post_cleanup = (count == 0)
                        self.set_cached_display_state('post_cleanup_check', is_post_cleanup)
                        print(f"FINAL FIX: Cached post-cleanup state - {is_post_cleanup}")
                    except Exception:
                        pass

            # Display appropriate message based on loading state and database content
            # FINAL FIX: Use cached display state to prevent frame-by-frame re-evaluation
            cached_state = self.get_cached_display_state('game_history_loading')
            
            if cached_state is not None:
                # Use cached state to prevent blinking
                loading_complete = cached_state
            else:
                # Evaluate once and cache the result
                loading_complete = (hasattr(self, '_stable_loading_complete') and self._stable_loading_complete) or \
                                 (hasattr(self, 'game_history_loading_complete') and self.game_history_loading_complete)
                self.set_cached_display_state('game_history_loading', loading_complete)
                print(f"FINAL FIX: Cached loading state - {loading_complete}")
            
            if loading_complete:
                # Loading is complete, check if database has games
                if hasattr(self, 'database_has_games') and self.database_has_games is False:
                    # Database is empty - no games have been played
                    message = "🎮 No games played yet - Start playing to see your game statistics here!"
                    message_color = (180, 180, 180)  # Light gray for informational message
                elif hasattr(self, 'database_has_games') and self.database_has_games is True:
                    # Database has games but none loaded (possibly due to filters)
                    message = "No games match the current filters"
                    message_color = (200, 200, 100)  # Yellow-ish for filter message
                else:
                    # Unknown state - fallback to database check
                    if is_post_cleanup:
                        message = "🎮 No games played yet - Start playing to see your game statistics here!"
                        message_color = (180, 180, 180)
                    else:
                        message = "No game data available"
                        message_color = (200, 200, 200)
            else:
                # Still loading
                message = "Loading game history..."
                message_color = WHITE

            no_data_text = no_data_font.render(message, True, message_color)
            no_data_rect = no_data_text.get_rect(center=(table_x + table_width//2, table_y + header_height + int(50 * self.scale_y)))
            self.screen.blit(no_data_text, no_data_rect)

        # FIXED: Draw pagination controls if needed (this was missing!)
        if self.total_history_pages > 1:
            pagination_y = table_y + table_height + int(10 * self.scale_y)
            self.draw_pagination_controls(table_x, pagination_y, table_width)
            print(f"DEBUG: Drew pagination controls - Page {self.history_page + 1} of {self.total_history_pages}")

        # Return the total height of the game history section
        total_height = table_height + title_font.get_height() + int(10 * self.scale_y)
        if self.total_history_pages > 1:
            total_height += int(40 * self.scale_y)  # Add space for pagination controls

        # ANTI-BLINK: Cache the height for throttled renders
        self._cached_game_history_height = total_height
        return total_height

    def _format_cell_value(self, game, col_id):
        """Format cell value based on column type."""
        value = game.get(col_id, "")

        if col_id == "session_id":
            # Return the session ID directly (already formatted as "Session-X")
            return str(value) if value else "N/A"
        elif col_id == "remainder":
            # Calculate remainder (modulo 10) of prize pool amount
            try:
                prize_pool = float(game.get("total_prize", 0))
                remainder = int(prize_pool) % 10
                return str(remainder)
            except (ValueError, TypeError):
                return "0"
        elif col_id == "stake" or col_id == "fee" or col_id == "total_prize":
            # Format currency with proper separators
            try:
                amount = float(value)
                return f"{amount:,.1f} ETB"
            except (ValueError, TypeError):
                return f"{value} ETB"

        elif col_id == "date_time":
            # Format date/time for better readability
            try:
                from datetime import datetime
                if isinstance(value, str):
                    dt = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                    return dt.strftime('%m/%d %H:%M')
                return str(value)
            except:
                return str(value)

        elif col_id == "commission_percent":
            # Format commission percentage
            try:
                commission = float(value)
                return f"{commission:.1f}%"
            except:
                return "N/A"

        elif col_id == "house":
            # ENHANCED: Format winner pattern based on username and game details
            username = game.get('username', '')

            if 'Game Reset' in username:
                return "Game Reset"
            elif 'No Winner' in username:
                return "No Winner"
            elif 'Cartella #' in username:
                # Extract cartella number and show as winner
                try:
                    cartella_num = username.replace('Cartella #', '').strip()
                    return f"Cartella #{cartella_num} Won"
                except:
                    return "Winner"
            else:
                # Try to format house pattern from value
                try:
                    if isinstance(value, str) and value.lower() != 'main house':
                        return str(value)
                    else:
                        # Default pattern names based on common bingo patterns
                        return "Main House"
                except:
                    return str(value)

        elif col_id == "status":
            # Capitalize status
            return str(value).title()

        else:
            # Default formatting with length limit
            value_str = str(value)
            if len(value_str) > 15:
                return value_str[:12] + "..."
            return value_str

    def _get_cell_color(self, col_id, value, game):
        """Get appropriate color for cell based on content."""
        if col_id == "session_id":
            # Session ID in light cyan
            return (150, 255, 255)
        elif col_id == "status":
            status = str(value).lower()
            if status == "won":
                return ETB_GREEN
            elif status == "lost":
                return (255, 100, 100)  # Light red
            else:
                return WHITE

        elif col_id in ["fee", "total_prize"]:
            # Highlight monetary values
            return (255, 215, 0)  # Gold color

        elif col_id == "remainder":
            # Highlight remainder values in orange with different brightness based on value
            try:
                value_int = int(value.strip())
                # Brighter orange for higher values
                brightness = min(255, 165 + value_int * 10)
                return (255, brightness, 0)  # Dynamic orange color
            except (ValueError, TypeError):
                return (255, 165, 0)  # Default orange color

        elif col_id == "stake":
            # Stake amount in light blue
            return (150, 200, 255)

        else:
            return WHITE

    def _calculate_responsive_column_widths(self, columns, table_width):
        """Calculate responsive column widths ensuring minimum widths and proper distribution."""
        responsive_columns = []
        total_min_width = sum(col.get("min_width", 50) for col in columns)

        # If table is too narrow, scale down proportionally
        if total_min_width > table_width:
            scale_factor = table_width / total_min_width
            for col in columns:
                min_width = col.get("min_width", 50)
                actual_width = max(30, int(min_width * scale_factor))  # Absolute minimum 30px
                responsive_col = col.copy()
                responsive_col["width"] = actual_width / table_width
                responsive_columns.append(responsive_col)
        else:
            # Use original proportions but enforce minimum widths
            remaining_width = table_width
            fixed_columns = []
            flexible_columns = []

            # First pass: handle columns that need minimum width enforcement
            for col in columns:
                min_width = col.get("min_width", 50)
                proposed_width = table_width * col["width"]

                if proposed_width < min_width:
                    # This column needs minimum width
                    fixed_columns.append((col, min_width))
                    remaining_width -= min_width
                else:
                    flexible_columns.append(col)

            # Second pass: distribute remaining width among flexible columns
            if flexible_columns and remaining_width > 0:
                total_flexible_proportion = sum(col["width"] for col in flexible_columns)
                for col in flexible_columns:
                    if total_flexible_proportion > 0:
                        actual_width = (col["width"] / total_flexible_proportion) * remaining_width
                    else:
                        actual_width = remaining_width / len(flexible_columns)

                    responsive_col = col.copy()
                    responsive_col["width"] = actual_width / table_width
                    responsive_columns.append(responsive_col)

            # Add fixed columns
            for col, width in fixed_columns:
                responsive_col = col.copy()
                responsive_col["width"] = width / table_width
                responsive_columns.append(responsive_col)

            # Sort to maintain original order
            responsive_columns.sort(key=lambda x: next(i for i, orig_col in enumerate(columns) if orig_col["id"] == x["id"]))

        return responsive_columns

    def _truncate_text_to_fit(self, text, font, max_width):
        """Truncate text to fit within the specified width, adding ellipsis if needed."""
        if not text:
            return ""

        text_str = str(text)

        # Check if text fits as-is
        text_surface = font.render(text_str, True, (255, 255, 255))
        if text_surface.get_width() <= max_width:
            return text_str

        # Binary search for the longest text that fits with ellipsis
        ellipsis = "..."
        ellipsis_width = font.render(ellipsis, True, (255, 255, 255)).get_width()
        available_width = max_width - ellipsis_width

        if available_width <= 0:
            return ellipsis

        # Start with a reasonable estimate
        chars_per_pixel = len(text_str) / text_surface.get_width()
        estimated_chars = int(available_width * chars_per_pixel)

        # Binary search for optimal length
        left, right = 0, min(estimated_chars + 5, len(text_str))
        best_length = 0

        while left <= right:
            mid = (left + right) // 2
            test_text = text_str[:mid]
            test_surface = font.render(test_text, True, (255, 255, 255))

            if test_surface.get_width() <= available_width:
                best_length = mid
                left = mid + 1
            else:
                right = mid - 1

        if best_length == 0:
            return ellipsis

        return text_str[:best_length] + ellipsis

    def calculate_remainder_for_date(self, date_str):
        """Calculate the remainder sum for a specific date"""
        remainder_sum = 0
        
        if hasattr(self, 'all_game_history') and self.all_game_history:
            for record in self.all_game_history:
                try:
                    # Check if this record is for the specified date
                    record_date = record.get("date_time", "").split(" ")[0]  # Extract date part
                    if record_date == date_str:
                        prize_pool = float(record.get("total_prize", 0))
                        remainder = int(prize_pool) % 10
                        remainder_sum += remainder
                except (ValueError, TypeError, IndexError):
                    pass
                    
        return remainder_sum
        
    def calculate_weekly_remainder(self, weekly_stats):
        """Calculate the total remainder for the current week"""
        weekly_remainder = 0
        
        # Calculate remainder for each day in the weekly stats
        for day_data in weekly_stats:
            day_date = day_data.get('date', '')
            day_remainder = self.calculate_remainder_for_date(day_date)
            weekly_remainder += day_remainder
            
        return weekly_remainder
        
    def calculate_total_remainder(self):
        """Calculate the total sum of remainders from all prize pools in the game history"""
        total_remainder = 0
        
        # Use all game history records, not just the current page
        if hasattr(self, 'all_game_history') and self.all_game_history:
            for record in self.all_game_history:
                try:
                    prize_pool = float(record.get("total_prize", 0))
                    remainder = int(prize_pool) % 10
                    total_remainder += remainder
                except (ValueError, TypeError):
                    pass
        # Fallback to current page if all_game_history is not available
        elif hasattr(self, 'game_history') and self.game_history:
            for record in self.game_history:
                try:
                    prize_pool = float(record.get("total_prize", 0))
                    remainder = int(prize_pool) % 10
                    total_remainder += remainder
                except (ValueError, TypeError):
                    pass
                    
        return total_remainder
        
    def draw_pagination_controls(self, x, y, width):
        """Draw pagination controls for game history"""
        # Draw page info
        page_font = self.get_font("Arial", self.scaled_font_size(14))
        page_text = page_font.render(f"Page {self.history_page + 1} of {self.total_history_pages}", True, WHITE)
        page_rect = page_text.get_rect(center=(x + width//2, y))
        self.screen.blit(page_text, page_rect)

        # Draw prev/next buttons
        button_width = int(80 * self.scale_x)
        button_height = int(30 * self.scale_y)

        # Previous button
        prev_rect = pygame.Rect(x, y - int(5 * self.scale_y), button_width, button_height)
        prev_color = LIGHT_BLUE if self.history_page > 0 else DARK_GRAY
        pygame.draw.rect(self.screen, prev_color, prev_rect, border_radius=5)

        prev_text = page_font.render("Previous", True, WHITE)
        prev_text_rect = prev_text.get_rect(center=prev_rect.center)
        self.screen.blit(prev_text, prev_text_rect)

        # Store hit area for previous button
        self.hit_areas["prev_page"] = prev_rect

        # Next button
        next_rect = pygame.Rect(x + width - button_width, y - int(5 * self.scale_y), button_width, button_height)
        next_color = LIGHT_BLUE if self.history_page < self.total_history_pages - 1 else DARK_GRAY
        pygame.draw.rect(self.screen, next_color, next_rect, border_radius=5)

        next_text = page_font.render("Next", True, WHITE)
        next_text_rect = next_text.get_rect(center=next_rect.center)
        self.screen.blit(next_text, next_text_rect)

        # Store hit area for next button
        self.hit_areas["next_page"] = next_rect

    def draw_credit_history_section(self, start_y, screen_height=None):
        """
        FLICKERING FIX: Draw credit history section with stable data management.

        Args:
            start_y: Y position to start drawing the section
            screen_height: Screen height for bounds checking (optional)
        """
        # FLICKERING FIX: Use stable credit history data to prevent blinking
        stable_credit_history = self.get_stable_ui_data('credit_history', [])

        # FLICKERING FIX: Only update stable data if we have new data and enough time has passed
        if hasattr(self, 'credit_history') and self.credit_history:
            if not stable_credit_history or self.should_update_section('credit_history'):
                self.set_stable_ui_data('credit_history', self.credit_history.copy())
                stable_credit_history = self.credit_history

        # FLICKERING FIX: Use stable data for all rendering operations
        if not stable_credit_history and hasattr(self, 'credit_history'):
            stable_credit_history = self.credit_history

        screen_width = self._layout['screen_width']
        if screen_height is None:
            screen_height = self._layout['screen_height']

        # Calculate available space for the section
        available_height = screen_height - start_y - self.notification_height - int(20 * self.scale_y)

        # FIXED: Section dimensions - ensure proper sizing for full display
        section_width = screen_width - int(40 * self.scale_x)
        # Increase minimum and optimal heights to prevent content cutoff
        min_section_height = int(150 * self.scale_y)
        optimal_section_height = int(300 * self.scale_y)
        section_height = min(optimal_section_height, max(min_section_height, available_height))
        section_x = int(20 * self.scale_x)
        section_y = start_y

        # Draw section header with toggle button (compact design)
        header_height = int(70 * self.scale_y)  # Increased to fit UUID info
        header_rect = pygame.Rect(section_x, section_y, section_width, header_height)

        # Header background with gradient
        header_surface = pygame.Surface((section_width, header_height))
        # Optimized gradient rendering - skip frames for performance
        gradient_step = max(1, header_height // 20)  # Reduce gradient steps
        for i in range(0, header_height, gradient_step):
            alpha = 1 - (i / header_height) * 0.3
            color = (
                int(40 * alpha),
                int(60 * alpha),
                int(90 * alpha)
            )
            pygame.draw.line(header_surface, color, (0, i), (section_width, i))

        self.screen.blit(header_surface, (section_x, section_y))
        pygame.draw.rect(self.screen, (80, 120, 160), header_rect, 2, border_radius=5)

        # Draw section title
        title_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        title_text = title_font.render("Credit Recharge History", True, WHITE)
        title_rect = title_text.get_rect(x=section_x + int(15 * self.scale_x), y=section_y + int(8 * self.scale_y))
        self.screen.blit(title_text, title_rect)

        # Draw current balance prominently
        current_balance = self.get_current_wallet_balance()
        balance_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        balance_text = balance_font.render(f"Balance: {current_balance:.1f} ETB", True, (255, 215, 0))
        balance_rect = balance_text.get_rect(right=section_x + section_width - int(15 * self.scale_x), y=section_y + int(8 * self.scale_y))
        self.screen.blit(balance_text, balance_rect)

        # FIXED: Draw full UUID info with copy functionality
        machine_uuid = self.get_machine_uuid()
        if machine_uuid:
            # Display full UUID without truncation with better formatting
            uuid_font = self.get_font("Courier New", self.scaled_font_size(10), bold=True)
            uuid_display = f"UUID: {machine_uuid}"
            uuid_text = uuid_font.render(uuid_display, True, (100, 255, 100))
            uuid_rect = uuid_text.get_rect(x=section_x + int(15 * self.scale_x), y=section_y + int(35 * self.scale_y))

            # Store UUID rect for click detection (expand clickable area)
            uuid_clickable_rect = pygame.Rect(uuid_rect.x - 5, uuid_rect.y - 2, uuid_rect.width + 10, uuid_rect.height + 4)
            self.hit_areas["uuid_copy"] = uuid_clickable_rect

            # Check if UUID is being hovered
            mouse_pos = pygame.mouse.get_pos()
            is_uuid_hovered = uuid_clickable_rect.collidepoint(mouse_pos)

            # Draw UUID with clickable background (enhanced with hover effect)
            if is_uuid_hovered:
                bg_color = (60, 100, 140)  # Brighter when hovered
                border_color = (120, 180, 240)
                text_color = (150, 255, 150)
            else:
                bg_color = (40, 60, 80)
                border_color = (100, 150, 200)
                text_color = (100, 255, 100)

            pygame.draw.rect(self.screen, bg_color, uuid_clickable_rect, border_radius=3)
            pygame.draw.rect(self.screen, border_color, uuid_clickable_rect, 2, border_radius=3)

            # Re-render text with hover color
            uuid_text = uuid_font.render(uuid_display, True, text_color)
            self.screen.blit(uuid_text, uuid_rect)

            # Draw copy icon next to UUID with enhanced styling
            copy_icon_x = uuid_rect.right + int(10 * self.scale_x)
            copy_icon_y = uuid_rect.centery - int(8 * self.scale_y)
            copy_icon_size = int(16 * self.scale_y)
            copy_icon_rect = pygame.Rect(copy_icon_x, copy_icon_y, copy_icon_size, copy_icon_size)

            # Store copy icon rect for click detection
            self.hit_areas["uuid_copy_icon"] = copy_icon_rect

            # Check if copy icon is being hovered
            is_copy_hovered = copy_icon_rect.collidepoint(mouse_pos)

            # Draw copy icon background with hover effect
            if is_copy_hovered:
                icon_bg_color = (80, 120, 160)
                icon_border_color = (120, 180, 240)
                icon_color = (255, 255, 255)
            else:
                icon_bg_color = (60, 100, 140)
                icon_border_color = (100, 150, 200)
                icon_color = (200, 200, 200)

            pygame.draw.rect(self.screen, icon_bg_color, copy_icon_rect, border_radius=2)
            pygame.draw.rect(self.screen, icon_border_color, copy_icon_rect, 1, border_radius=2)

            # Draw copy icon (two overlapping rectangles)
            # Main rectangle
            main_rect = pygame.Rect(copy_icon_x + 2, copy_icon_y + 4, 8, 8)
            pygame.draw.rect(self.screen, icon_color, main_rect, 1)
            # Shadow rectangle
            shadow_rect = pygame.Rect(copy_icon_x + 6, copy_icon_y + 2, 8, 8)
            pygame.draw.rect(self.screen, icon_color, shadow_rect, 1)

            # Add tooltip text (only show when hovered)
            if is_copy_hovered or is_uuid_hovered:
                tooltip_font = self.get_font("Arial", self.scaled_font_size(9), bold=True)
                tooltip_text = tooltip_font.render("Click to copy UUID", True, (255, 255, 100))
                tooltip_rect = tooltip_text.get_rect(x=copy_icon_rect.right + 5, centery=copy_icon_rect.centery)

                # Draw tooltip background
                tooltip_bg = pygame.Rect(tooltip_rect.x - 3, tooltip_rect.y - 1, tooltip_rect.width + 6, tooltip_rect.height + 2)
                pygame.draw.rect(self.screen, (40, 40, 40), tooltip_bg, border_radius=2)
                pygame.draw.rect(self.screen, (100, 100, 100), tooltip_bg, 1, border_radius=2)

                self.screen.blit(tooltip_text, tooltip_rect)

        # Draw toggle button
        toggle_size = int(30 * self.scale_y)
        toggle_x = balance_rect.left - toggle_size - int(10 * self.scale_x)
        toggle_y = section_y + (header_height - toggle_size) // 2
        toggle_rect = pygame.Rect(toggle_x, toggle_y, toggle_size, toggle_size)

        # Store hit area for toggle button
        self.hit_areas["credit_history_toggle"] = toggle_rect

        # Draw toggle button
        toggle_color = (100, 150, 200) if self.credit_history_visible else (80, 80, 80)
        pygame.draw.rect(self.screen, toggle_color, toggle_rect, border_radius=5)

        # Draw toggle icon (arrow)
        arrow_color = WHITE
        center_x, center_y = toggle_rect.center
        if self.credit_history_visible:
            # Down arrow (expanded)
            points = [
                (center_x - 8, center_y - 4),
                (center_x + 8, center_y - 4),
                (center_x, center_y + 4)
            ]
        else:
            # Right arrow (collapsed)
            points = [
                (center_x - 4, center_y - 8),
                (center_x - 4, center_y + 8),
                (center_x + 4, center_y)
            ]
        pygame.draw.polygon(self.screen, arrow_color, points)

        # Only draw the table if the section is visible
        if not self.credit_history_visible:
            return

        # Draw credit history table
        table_y = section_y + header_height
        table_height = section_height - header_height

        self.draw_credit_history_table(section_x, table_y, section_width, table_height)

    def draw_credit_history_table(self, table_x, table_y, table_width, table_height):
        """
        Draw the credit history table with professional styling.
        """
        # Draw table background
        table_rect = pygame.Rect(table_x, table_y, table_width, table_height)
        pygame.draw.rect(self.screen, CARD_BG, table_rect, border_radius=5)

        # Define credit history columns
        columns = [
            {"id": "transaction_id", "name": "TRANSACTION ID", "width": 0.15, "align": "center"},
            {"id": "recharge_date", "name": "RECHARGE DATE", "width": 0.15, "align": "center"},
            {"id": "credit_amount", "name": "RECHARGED AMOUNT", "width": 0.12, "align": "right"},
            {"id": "expiry_date", "name": "EXPIRY DATE", "width": 0.12, "align": "center"},
            {"id": "share_amount", "name": "SHARE %", "width": 0.08, "align": "center"},
            {"id": "payment_method", "name": "METHOD", "width": 0.12, "align": "center"},
            {"id": "status", "name": "STATUS", "width": 0.1, "align": "center"},
            {"id": "remaining_date", "name": "REMAINING DATE", "width": 0.16, "align": "right"}
        ]

        # Calculate row dimensions (compact)
        row_height = int(25 * self.scale_y)
        header_height = int(30 * self.scale_y)

        # Draw table header
        header_rect = pygame.Rect(table_x, table_y, table_width, header_height)

        # Create gradient header background
        header_surface = pygame.Surface((table_width, header_height))
        # Optimized gradient rendering - skip frames for performance
        gradient_step = max(1, header_height // 20)  # Reduce gradient steps
        for i in range(0, header_height, gradient_step):
            alpha = 1 - (i / header_height) * 0.3
            color = (
                int(TABLE_HEADER_BG[0] * alpha),
                int(TABLE_HEADER_BG[1] * alpha),
                int(TABLE_HEADER_BG[2] * alpha)
            )
            pygame.draw.line(header_surface, color, (0, i), (table_width, i))

        self.screen.blit(header_surface, (table_x, table_y))
        pygame.draw.rect(self.screen, (60, 80, 120), header_rect, 2, border_radius=5)

        # Draw column headers
        header_font = self.get_font("Arial", self.scaled_font_size(11), bold=True)
        x_offset = table_x

        for column in columns:
            col_width = int(table_width * column["width"])
            header_text = header_font.render(column["name"], True, WHITE)

            # Align header text
            if column["align"] == "center":
                header_rect = header_text.get_rect(centerx=x_offset + col_width//2, centery=table_y + header_height//2)
            elif column["align"] == "right":
                header_rect = header_text.get_rect(right=x_offset + col_width - 10, centery=table_y + header_height//2)
            else:  # left align
                header_rect = header_text.get_rect(left=x_offset + 10, centery=table_y + header_height//2)

            self.screen.blit(header_text, header_rect)

            # Draw column separator
            if x_offset + col_width < table_x + table_width:
                separator_x = x_offset + col_width
                pygame.draw.line(self.screen, (80, 100, 140),
                               (separator_x, table_y + 5),
                               (separator_x, table_y + header_height - 5), 1)

            x_offset += col_width

        # Draw credit history rows
        if hasattr(self, 'credit_history') and self.credit_history:
            # Group credit history by date (similar to game history)
            processed_credit_history = self._group_credit_history_by_date(self.credit_history)

            # Clear and rebuild credit table rows for hit detection
            self.credit_table_rows = []

            row_font = self.get_font("Arial", self.scaled_font_size(10))
            row_font_bold = self.get_font("Arial", self.scaled_font_size(10), bold=True)
            separator_font = self.get_font("Arial", self.scaled_font_size(12), bold=True)

            visible_row_count = 0
            max_visible_rows = (table_height - header_height) // row_height

            for i, record in enumerate(processed_credit_history):
                if visible_row_count >= max_visible_rows:
                    break

                # Calculate row position
                row_y = table_y + header_height + visible_row_count * row_height

                # Create row rectangle for hit detection
                row_rect = pygame.Rect(table_x, row_y, table_width, row_height)
                self.credit_table_rows.append(row_rect)

                # Check if this is a date separator
                if record.get('is_date_separator', False):
                    # Draw date separator row
                    self._draw_date_separator(table_x, row_y, table_width, row_height,
                                            record['display_text'], separator_font)
                else:
                    # Draw regular credit row with highlighting
                    base_color = TABLE_ROW_BG_1 if visible_row_count % 2 == 0 else TABLE_ROW_BG_2

                    # Apply highlighting effects
                    if visible_row_count == self.credit_selected_row_index:
                        row_color = (60, 120, 200)  # Bright blue
                        border_color = (100, 150, 255)
                        border_width = 3
                    elif visible_row_count == self.credit_hovered_row_index:
                        row_color = tuple(min(255, c + 30) for c in base_color)
                        border_color = (120, 140, 180)
                        border_width = 2
                    else:
                        row_color = base_color
                        border_color = (70, 80, 100)
                        border_width = 1

                    # Draw row background
                    pygame.draw.rect(self.screen, row_color, row_rect)

                    # Draw row border
                    if border_width > 1:
                        pygame.draw.rect(self.screen, border_color, row_rect, border_width)
                    else:
                        pygame.draw.line(self.screen, border_color,
                                       (table_x, row_y + row_height),
                                       (table_x + table_width, row_y + row_height), border_width)

                    # Draw row data
                    x_offset = table_x

                    for column in columns:
                        col_width = int(table_width * column["width"])
                        col_id = column["id"]

                        # Get and format value for this column
                        value = self._format_credit_cell_value(record, col_id)

                        # Choose font and color based on column
                        font = row_font_bold if col_id in ["credit_amount", "remaining_date", "status"] else row_font
                        text_color = self._get_credit_cell_color(col_id, value, record)

                        # Render text
                        cell_text = font.render(value, True, text_color)

                        # Align text based on column alignment
                        if column["align"] == "center":
                            cell_rect = cell_text.get_rect(centerx=x_offset + col_width//2, centery=row_y + row_height//2)
                        elif column["align"] == "right":
                            cell_rect = cell_text.get_rect(right=x_offset + col_width - 10, centery=row_y + row_height//2)
                        else:  # left align
                            cell_rect = cell_text.get_rect(left=x_offset + 10, centery=row_y + row_height//2)

                        self.screen.blit(cell_text, cell_rect)
                        x_offset += col_width

                visible_row_count += 1

        else:
            # Draw "No data" message when no credit history is available
            no_data_font = self.get_font("Arial", self.scaled_font_size(16))
            no_data_text = no_data_font.render("No credit recharge history available", True, WHITE)
            no_data_rect = no_data_text.get_rect(center=(table_x + table_width//2, table_y + header_height + int(50 * self.scale_y)))
            self.screen.blit(no_data_text, no_data_rect)

    def _group_credit_history_by_date(self, credit_history):
        """
        Group credit history by date and add date separators.

        Args:
            credit_history: List of credit history records

        Returns:
            List of processed records including date separators
        """
        if not credit_history:
            return []

        from datetime import datetime
        from collections import defaultdict

        # Group credits by date
        credits_by_date = defaultdict(list)

        for record in credit_history:
            try:
                # Extract date from recharge_date field
                recharge_date_str = record.get('recharge_date', '')
                if isinstance(recharge_date_str, str) and recharge_date_str:
                    # Parse the date (assuming format like "2024-01-15 14:30")
                    try:
                        dt = datetime.strptime(recharge_date_str, '%Y-%m-%d %H:%M')
                    except ValueError:
                        # Try alternative format
                        dt = datetime.strptime(recharge_date_str, '%Y-%m-%d %H:%M:%S')

                    date_key = dt.strftime('%Y-%m-%d')
                    day_name = dt.strftime('%A')  # Get day of week
                    credits_by_date[date_key].append({
                        **record,
                        'parsed_date': dt,
                        'day_name': day_name
                    })
                else:
                    # Handle records without proper date
                    credits_by_date['unknown'].append({
                        **record,
                        'parsed_date': None,
                        'day_name': 'Unknown'
                    })
            except Exception as e:
                print(f"Error parsing date for credit record: {e}")
                credits_by_date['unknown'].append({
                    **record,
                    'parsed_date': None,
                    'day_name': 'Unknown'
                })

        # Sort dates chronologically (most recent first)
        sorted_dates = sorted(credits_by_date.keys(), reverse=True)
        if 'unknown' in sorted_dates:
            # Move unknown to the end
            sorted_dates.remove('unknown')
            sorted_dates.append('unknown')

        # Build the final list with date separators
        processed_history = []

        for date_key in sorted_dates:
            credits_for_date = credits_by_date[date_key]

            # Sort credits within the date by time (most recent first)
            credits_for_date.sort(key=lambda x: x.get('parsed_date') or datetime.min, reverse=True)

            # Add date separator
            if credits_for_date:
                day_name = credits_for_date[0]['day_name']
                # Format: *********Day:[in Red Bold color] *************
                separator = {
                    'is_date_separator': True,
                    'date_key': date_key,
                    'day_name': day_name,
                    'display_text': f"*********{day_name}:*************",
                    'formatted_date': date_key  # Store the actual date for reference
                }
                processed_history.append(separator)

                # Add credit records
                for record in credits_for_date:
                    record_copy = record.copy()
                    record_copy['is_date_separator'] = False
                    processed_history.append(record_copy)

        return processed_history

    def copy_uuid_to_clipboard(self):
        """
        Copy the machine UUID to clipboard with user feedback while maintaining window focus.
        """
        try:
            machine_uuid = self.get_machine_uuid()
            if machine_uuid:
                # Store current pygame window info for focus restoration
                pygame_window_id = None
                try:
                    # Get pygame window handle if available
                    import os
                    if hasattr(os, 'environ'):
                        pygame_window_id = os.environ.get('SDL_WINDOWID')
                except:
                    pass

                clipboard_success = False
                method_used = ""

                # Method 1: Try Windows clip command first (doesn't interfere with window focus)
                try:
                    import subprocess
                    import sys
                    if sys.platform == "win32":
                        # Use Windows clip command - this is the most reliable on Windows
                        process = subprocess.Popen(['clip'], stdin=subprocess.PIPE, text=True,
                                                 creationflags=subprocess.CREATE_NO_WINDOW)
                        process.communicate(input=machine_uuid)
                        if process.returncode == 0:
                            clipboard_success = True
                            method_used = "Windows clip"
                            print(f"UUID copied to clipboard using Windows clip: {machine_uuid}")
                except Exception as e:
                    print(f"Windows clip method failed: {e}")

                # Method 2: Try using pyperclip if available (doesn't create windows)
                if not clipboard_success:
                    try:
                        import pyperclip
                        pyperclip.copy(machine_uuid)
                        clipboard_success = True
                        method_used = "pyperclip"
                        print(f"UUID copied to clipboard using pyperclip: {machine_uuid}")
                    except Exception as e:
                        print(f"Pyperclip method failed: {e}")

                # Method 3: Try using tkinter with focus preservation (last resort)
                if not clipboard_success:
                    try:
                        import tkinter as tk

                        # Create tkinter root but keep it completely hidden and non-interfering
                        root = tk.Tk()
                        root.withdraw()  # Hide immediately
                        root.attributes('-alpha', 0)  # Make completely transparent
                        root.attributes('-topmost', False)  # Don't bring to front

                        # Perform clipboard operation quickly
                        root.clipboard_clear()
                        root.clipboard_append(machine_uuid)
                        root.update_idletasks()  # Minimal update

                        # Destroy immediately
                        root.destroy()

                        clipboard_success = True
                        method_used = "tkinter (focus-safe)"
                        print(f"UUID copied to clipboard using focus-safe tkinter: {machine_uuid}")

                        # Force pygame window to regain focus using dedicated method
                        self.ensure_window_focus()

                    except Exception as e:
                        print(f"Focus-safe tkinter clipboard method failed: {e}")

                # Ensure window focus is maintained after any clipboard operation
                self.ensure_window_focus()

                # Provide user feedback
                if clipboard_success:
                    self.show_message(f"UUID copied to clipboard ({method_used}): {machine_uuid[:8]}...{machine_uuid[-8:]}", "success")
                else:
                    # Fallback: show the UUID in a message for manual copying
                    self.show_message(f"Copy this UUID manually: {machine_uuid}", "info")
                    print(f"All clipboard methods failed. UUID to copy manually: {machine_uuid}")

            else:
                self.show_message("UUID not available", "error")

        except Exception as e:
            print(f"Error copying UUID to clipboard: {e}")
            self.show_message("Error copying UUID to clipboard", "error")

    def ensure_window_focus(self):
        """
        Ensure the pygame window maintains focus and visibility.
        """
        try:
            # Refresh the pygame display
            pygame.display.flip()

            # Try to bring pygame window to front on Windows
            import sys
            if sys.platform == "win32":
                try:
                    import ctypes

                    # Get pygame window handle
                    wm_info = pygame.display.get_wm_info()
                    if wm_info and "window" in wm_info:
                        hwnd = wm_info["window"]
                        if hwnd:
                            # Multiple approaches to ensure window stays visible
                            user32 = ctypes.windll.user32

                            # Method 1: Standard window restoration
                            user32.ShowWindow(hwnd, 9)  # SW_RESTORE
                            user32.SetForegroundWindow(hwnd)
                            user32.BringWindowToTop(hwnd)

                            # Method 2: Force window to be active
                            user32.SetActiveWindow(hwnd)

                            # Method 3: Ensure window is not minimized
                            user32.ShowWindow(hwnd, 1)  # SW_SHOWNORMAL

                            print("Successfully restored pygame window focus")
                            return True

                except Exception as e:
                    print(f"Windows focus restoration failed: {e}")

            # For other platforms or if Windows method fails
            try:
                # Force a pygame event to keep window active
                pygame.event.post(pygame.event.Event(pygame.USEREVENT))
                pygame.display.update()
                return True
            except Exception as e:
                print(f"Generic focus restoration failed: {e}")

        except Exception as e:
            print(f"Window focus restoration error: {e}")

        return False

    def _format_credit_cell_value(self, record, col_id):
        """Format credit cell value based on column type."""
        value = record.get(col_id, "")

        if col_id == "transaction_id":
            # Return the transaction ID directly
            return str(value) if value else "N/A"
        elif col_id == "credit_amount":
            # Format currency with proper separators
            try:
                amount = float(value)
                return f"{amount:,.1f} ETB"
            except (ValueError, TypeError):
                return f"{value} ETB"
        elif col_id == "remaining_date":
            # Return the days remaining text directly (already formatted)
            return str(value) if value else "N/A"
        elif col_id == "recharge_date":
            # Format date/time for better readability
            try:
                from datetime import datetime
                if isinstance(value, str):
                    try:
                        dt = datetime.strptime(value, '%Y-%m-%d %H:%M')
                    except ValueError:
                        dt = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                    return dt.strftime('%m/%d %H:%M')
                return str(value)
            except:
                return str(value)
        elif col_id == "expiry_date":
            # Format expiry date
            try:
                if value == "No Expiry" or not value:
                    return "No Expiry"
                from datetime import datetime
                if isinstance(value, str):
                    dt = datetime.strptime(value, '%Y-%m-%d')
                    return dt.strftime('%m/%d/%Y')
                return str(value)
            except:
                return str(value)
        elif col_id == "share_amount":
            # Format share percentage
            try:
                share = float(value)
                return f"{share:.0f}%"
            except (ValueError, TypeError):
                return f"{value}%"
        elif col_id == "status":
            # Capitalize status
            return str(value).title()
        else:
            # Default formatting with length limit
            value_str = str(value)
            if len(value_str) > 15:
                return value_str[:12] + "..."
            return value_str

    def _get_credit_cell_color(self, col_id, value, record):
        """Get appropriate color for credit cell based on content."""
        if col_id == "transaction_id":
            # Transaction ID in light cyan
            return (150, 255, 255)
        elif col_id == "status":
            status = str(value).lower()
            if status == "active":
                return ETB_GREEN
            elif status == "expired":
                return (255, 100, 100)  # Light red
            elif status == "used":
                return (200, 200, 200)  # Gray
            else:
                return WHITE
        elif col_id == "credit_amount":
            # Highlight monetary values
            return (255, 215, 0)  # Gold color
        elif col_id == "remaining_date":
            # Bold red color for remaining days
            return (255, 0, 0)  # Red color
        elif col_id == "share_amount":
            # Share percentage in light blue
            return (150, 200, 255)
        else:
            return WHITE

    @time_operation("draw_background")
    def draw_background(self, screen_width, screen_height):
        """Draw an optimized background with gradient and subtle patterns"""
        # Create a surface for the background if not already created or if screen size changed
        if not hasattr(self, '_bg_surface') or self._bg_surface.get_size() != (screen_width, screen_height):
            print("Creating new background surface")
            self._bg_surface = pygame.Surface((screen_width, screen_height))

            # Use a cache key based on dimensions
            bg_cache_key = f"bg_{screen_width}_{screen_height}"

            # Check if we have this background cached on disk
            cache_path = os.path.join('data', 'cache', f"{bg_cache_key}.png")
            if os.path.exists(cache_path):
                try:
                    # Load from cache
                    cached_bg = pygame.image.load(cache_path)
                    self._bg_surface.blit(cached_bg, (0, 0))
                    print("Loaded background from cache")
                except Exception as e:
                    print(f"Error loading cached background: {e}")
                    # Fall back to generating a new one
                    self._generate_background(screen_width, screen_height)

                    # Save to cache for next time
                    try:
                        # Ensure cache directory exists
                        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
                        pygame.image.save(self._bg_surface, cache_path)
                        print("Saved background to cache")
                    except Exception as e:
                        print(f"Error saving background to cache: {e}")
            else:
                # Generate new background
                self._generate_background(screen_width, screen_height)

                # Save to cache for next time
                try:
                    # Ensure cache directory exists
                    os.makedirs(os.path.dirname(cache_path), exist_ok=True)
                    pygame.image.save(self._bg_surface, cache_path)
                    print("Saved background to cache")
                except Exception as e:
                    print(f"Error saving background to cache: {e}")

        # Blit the cached background
        self.screen.blit(self._bg_surface, (0, 0))

    @time_operation("generate_background")
    def _generate_background(self, screen_width, screen_height):
        """Generate a new background surface with gradient and patterns"""
        # Draw gradient background - more efficient with larger steps
        step_size = 8  # Increased step size for better performance
        for y in range(0, screen_height, step_size):
            # Calculate gradient color (dark blue to slightly lighter blue)
            ratio = y / screen_height
            r = 20 * (1 - ratio) + 35 * ratio
            g = 25 * (1 - ratio) + 45 * ratio
            b = 50 * (1 - ratio) + 70 * ratio

            # Draw thicker lines to fill the gaps
            pygame.draw.rect(self._bg_surface, (r, g, b),
                           (0, y, screen_width, step_size))

        # Add subtle grid pattern - with larger spacing for better performance
        grid_color = (50, 70, 100)  # Solid color instead of alpha for better performance
        grid_spacing = 120  # Increased spacing for better performance

        # Draw vertical grid lines - only every other line for better performance
        for x in range(0, screen_width, grid_spacing):
            pygame.draw.line(self._bg_surface, grid_color, (x, 0), (x, screen_height), 1)

        # Draw horizontal grid lines - only every other line for better performance
        for y in range(0, screen_height, grid_spacing):
            pygame.draw.line(self._bg_surface, grid_color, (0, y), (screen_width, y), 1)

        # Add simplified vignette effect (darker corners) - much more efficient
        # Create a radial gradient using rectangles instead of pixel-by-pixel drawing
        vignette_steps = 4  # Reduced number of steps for better performance
        max_alpha = 60  # Maximum alpha value

        for i in range(vignette_steps):
            # Calculate size of this rectangle
            ratio = i / vignette_steps
            rect_width = int(screen_width * ratio)
            rect_height = int(screen_height * ratio)

            # Calculate alpha for this step
            alpha = int(max_alpha * (1 - ratio))

            if alpha > 0:
                # Create a surface with the right alpha
                vignette_rect = pygame.Surface((rect_width, rect_height), pygame.SRCALPHA)
                vignette_rect.fill((0, 0, 0, alpha))

                # Position in center
                pos_x = (screen_width - rect_width) // 2
                pos_y = (screen_height - rect_height) // 2

                # Draw the rectangle
                self._bg_surface.blit(vignette_rect, (pos_x, pos_y))

    def draw_navigation_bar(self, screen_width, _):
        # We don't use screen_height but keep the parameter for consistency with other methods
        """Draw a modern compact navigation menu at the top right of the screen"""
        # Configuration for the modern navbar
        nav_height = int(40 * self.scale_y)
        nav_width = int(screen_width * 0.5)  # 50% of screen width, positioned at right
        nav_x = screen_width - nav_width

        # Slightly transparent background for the nav bar
        nav_rect = pygame.Rect(nav_x, 0, nav_width, nav_height)

        # Create surface with compatible format - handle the case when SRCALPHA is not supported
        try:
            nav_surface = pygame.Surface((nav_rect.width, nav_rect.height), pygame.SRCALPHA)
            nav_surface.fill((NAV_BAR_BG[0], NAV_BAR_BG[1], NAV_BAR_BG[2], 220))  # Semi-transparent
            self.screen.blit(nav_surface, nav_rect)
        except Exception as e:
            # Fallback to regular surface if SRCALPHA is not supported
            print(f"Using fallback for nav bar surface: {e}")
            nav_surface = pygame.Surface((nav_rect.width, nav_rect.height))
            nav_surface.fill((NAV_BAR_BG[0], NAV_BAR_BG[1], NAV_BAR_BG[2]))  # Solid color
            self.screen.blit(nav_surface, nav_rect)

        # Add a subtle bottom border
        pygame.draw.line(self.screen, (60, 80, 100),
                        (nav_x, nav_height-1),
                        (screen_width, nav_height-1), 1)

        # Navigation items with modern icons and fallback text
        nav_items = [
            {"id": "play", "text": "Game", "icon": "🎮", "fallback": "▶"},
            {"id": "stats", "text": "Stats", "icon": "📊", "fallback": "📈"},
            {"id": "settings", "text": "Settings", "icon": "⚙️", "fallback": "⚙"},
            {"id": "help", "text": "Help", "icon": "❓", "fallback": "?"},
            {"id": "refresh", "text": "Refresh", "icon": "🔄", "fallback": "↻"}
        ]

        # Calculate positioning - more compact layout
        item_width = int(nav_width / len(nav_items))  # Convert to int
        item_x_start = nav_x
        nav_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)  # Use cached font

        # Draw each navigation item
        for i, item in enumerate(nav_items):
            # Calculate item position
            item_x = item_x_start + i * item_width
            item_rect = pygame.Rect(item_x, 0, item_width, nav_height)

            # Store hit area for this nav item
            self.hit_areas[f"nav_{item['id']}"] = item_rect

            # Check if this is the active nav item
            is_active = (item["id"] == self.active_nav)

            # Get button state for animations
            btn_state = self.button_states.get(f"nav_{item['id']}", {
                "hover": False,
                "click": False,
                "hover_alpha": 0
            })

            # Draw highlight for active or hovered item
            if is_active:
                # Active item gets a solid highlight
                highlight_color = (60, 130, 200, 200)  # Blue highlight
                pygame.draw.rect(self.screen, highlight_color, item_rect)
            elif btn_state["hover_alpha"] > 0:
                # Hovered item gets a semi-transparent highlight
                # Safer approach for highlight surface with fallback
                try:
                    highlight_color = (60, 130, 200, btn_state["hover_alpha"])
                    highlight_surface = pygame.Surface((item_rect.width, item_rect.height), pygame.SRCALPHA)
                    highlight_surface.fill(highlight_color)
                    self.screen.blit(highlight_surface, item_rect)
                except Exception as e:
                    # Fallback to non-alpha blend if SRCALPHA fails
                    print(f"Using fallback for highlight surface: {e}")
                    highlight_color = (60, 130, 200)
                    pygame.draw.rect(self.screen, highlight_color, item_rect, 1)  # Draw outline instead

            # Draw text and icon
            text_color = WHITE if is_active else LIGHT_GRAY

            # Calculate icon and text positions
            icon_size = int(20 * min(self.scale_x, self.scale_y))  # Increased size for better visibility
            icon_x = int(item_x + item_width / 2)  # Convert to int
            icon_y = int(nav_height / 2 - icon_size / 2)  # Convert to int

            # Try to draw emoji icon first, then fallback to custom drawing
            icon_drawn = False

            # Attempt to render emoji icon
            try:
                icon_font = self.get_font("Arial", int(icon_size * 0.8))
                icon_text = icon_font.render(item["icon"], True, text_color)
                if icon_text.get_width() > 0 and icon_text.get_height() > 0:
                    # Emoji rendered successfully
                    icon_rect = icon_text.get_rect(centerx=icon_x, centery=icon_y + icon_size//2)
                    self.screen.blit(icon_text, icon_rect)
                    icon_drawn = True
            except Exception as e:
                print(f"Emoji rendering failed for {item['id']}: {e}")

            # If emoji failed, try fallback character
            if not icon_drawn:
                try:
                    fallback_font = self.get_font("Arial", int(icon_size * 0.9), bold=True)
                    fallback_text = fallback_font.render(item["fallback"], True, text_color)
                    if fallback_text.get_width() > 0 and fallback_text.get_height() > 0:
                        fallback_rect = fallback_text.get_rect(centerx=icon_x, centery=icon_y + icon_size//2)
                        self.screen.blit(fallback_text, fallback_rect)
                        icon_drawn = True
                except Exception as e:
                    print(f"Fallback character rendering failed for {item['id']}: {e}")

            # If both emoji and fallback failed, draw custom icon
            if not icon_drawn:
                if item["id"] == "play":
                    # Game controller icon
                    pygame.draw.rect(self.screen, text_color,
                                   pygame.Rect(icon_x - icon_size/2, icon_y + icon_size/4,
                                              icon_size, icon_size/2))
                    pygame.draw.circle(self.screen, text_color,
                                     (int(icon_x - icon_size/4), int(icon_y + icon_size/4)),
                                     int(icon_size/6))
                    pygame.draw.circle(self.screen, text_color,
                                     (int(icon_x + icon_size/4), int(icon_y + icon_size/4)),
                                     int(icon_size/6))

                elif item["id"] == "stats":
                    # Stats bar chart icon
                    bar_width = icon_size/5
                    # Draw three bars of increasing height
                    pygame.draw.rect(self.screen, text_color,
                                   pygame.Rect(icon_x - icon_size/2, icon_y,
                                              bar_width, icon_size/2))
                    pygame.draw.rect(self.screen, text_color,
                                   pygame.Rect(icon_x - icon_size/6, icon_y - icon_size/4,
                                              bar_width, icon_size*3/4))
                    pygame.draw.rect(self.screen, text_color,
                                   pygame.Rect(icon_x + icon_size/6, icon_y - icon_size/2,
                                              bar_width, icon_size))

                elif item["id"] == "settings":
                    # Settings gear icon
                    center = (int(icon_x), int(icon_y + icon_size/2))
                    outer_radius = int(icon_size/2)
                    inner_radius = int(icon_size/4)
                    teeth = 8

                    for i in range(teeth):
                        angle = 2 * math.pi * i / teeth
                        next_angle = 2 * math.pi * (i + 0.5) / teeth

                        # Outer point
                        x1 = center[0] + outer_radius * math.cos(angle)
                        y1 = center[1] + outer_radius * math.sin(angle)

                        # Inner point
                        x2 = center[0] + inner_radius * math.cos(next_angle)
                        y2 = center[1] + inner_radius * math.sin(next_angle)

                        # Next outer point
                        x3 = center[0] + outer_radius * math.cos(next_angle + math.pi/teeth)
                        y3 = center[1] + outer_radius * math.sin(next_angle + math.pi/teeth)

                        # Draw triangle for gear tooth
                        pygame.draw.polygon(self.screen, text_color, [(x1, y1), (x2, y2), (x3, y3)])

                    # Draw center circle
                    pygame.draw.circle(self.screen, text_color, center, int(icon_size/6))

                elif item["id"] == "help":
                    # Help question mark icon
                    center = (int(icon_x), int(icon_y + icon_size/2))
                    radius = int(icon_size/2)

                    # Draw circle
                    pygame.draw.circle(self.screen, text_color, center, radius, 2)

                    # Draw question mark
                    question_font = pygame.font.SysFont("Arial", int(icon_size * 0.8), bold=True)
                    question_text = question_font.render("?", True, text_color)
                    question_rect = question_text.get_rect(center=center)
                    self.screen.blit(question_text, question_rect)

                elif item["id"] == "refresh":
                    # Refresh circular arrow icon
                    center = (int(icon_x), int(icon_y + icon_size/2))
                    radius = int(icon_size/2)

                    # Draw circular arrow
                    start_angle = math.pi/4
                    end_angle = 2*math.pi - math.pi/4

                    # Draw arc
                    rect = pygame.Rect(center[0] - radius, center[1] - radius, radius*2, radius*2)
                    pygame.draw.arc(self.screen, text_color, rect, start_angle, end_angle, 2)

                    # Draw arrowhead
                    arrow_size = radius/3
                    arrow_angle = start_angle - math.pi/8
                    arrow_x = center[0] + (radius * math.cos(arrow_angle))
                    arrow_y = center[1] + (radius * math.sin(arrow_angle))

                    # Calculate points for arrowhead
                    p1 = (arrow_x, arrow_y)
                    p2 = (arrow_x + arrow_size * math.cos(arrow_angle - 2.5),
                          arrow_y + arrow_size * math.sin(arrow_angle - 2.5))
                    p3 = (arrow_x + arrow_size * math.cos(arrow_angle + 0.5),
                          arrow_y + arrow_size * math.sin(arrow_angle + 0.5))

                    # Draw arrowhead
                    pygame.draw.polygon(self.screen, text_color, [p1, p2, p3])

            # Draw text below icon
            text_surf = nav_font.render(item["text"], True, text_color)
            text_rect = text_surf.get_rect(centerx=int(item_x + item_width/2),
                                         centery=nav_height - int(10 * self.scale_y))
            self.screen.blit(text_surf, text_rect)

    def draw_wow_bingo_header(self):
        """Draw the WOW Games header consistent with the main window"""
        # Use the common header implementation
        return draw_wow_bingo_header(self.screen, self.scale_x, self.scale_y)

    def draw_toast_message(self):
        """Draw a toast message at the bottom of the screen - optimized version"""
        if not self.message:
            return

        # Check if we have a cached toast message
        if hasattr(self, '_toast_cache') and self.message in self._toast_cache:
            try:
                # Get cached toast surface and position
                toast_surface, toast_x, toast_y = self._toast_cache[self.message]
            except Exception as e:
                print(f"Error using cached toast: {e}")
                # Force regeneration by skipping the cache
                if hasattr(self, '_toast_cache'):
                    self._toast_cache.pop(self.message, None)
                return self.draw_toast_message()  # Recursive call to regenerate

            # Update alpha based on timer
            if self.message_type == "error":
                color_alpha = min(255, self.message_timer * 3)
                pygame.draw.rect(
                    toast_surface,
                    (200, 50, 50, color_alpha),
                    pygame.Rect(0, 0, toast_surface.get_width(), toast_surface.get_height()),
                    border_radius=int(10 * min(self.scale_x, self.scale_y))
                )
            elif self.message_type == "success":
                color_alpha = min(255, self.message_timer * 3)
                pygame.draw.rect(
                    toast_surface,
                    (50, 200, 50, color_alpha),
                    pygame.Rect(0, 0, toast_surface.get_width(), toast_surface.get_height()),
                    border_radius=int(10 * min(self.scale_x, self.scale_y))
                )
            else:  # info
                color_alpha = min(255, self.message_timer * 3)
                pygame.draw.rect(
                    toast_surface,
                    (50, 120, 200, color_alpha),
                    pygame.Rect(0, 0, toast_surface.get_width(), toast_surface.get_height()),
                    border_radius=int(10 * min(self.scale_x, self.scale_y))
                )

            # Draw to screen
            self.screen.blit(toast_surface, (toast_x, toast_y))
            return

        screen_width, screen_height = self.screen.get_size()

        # Determine color based on message type
        if self.message_type == "error":
            color = (200, 50, 50, min(255, self.message_timer * 3))
        elif self.message_type == "success":
            color = (50, 200, 50, min(255, self.message_timer * 3))
        else:  # info
            color = (50, 120, 200, min(255, self.message_timer * 3))

        # Create message surface - use cached font
        font = self.get_font("Arial", self.scaled_font_size(18))
        text = font.render(self.message, True, WHITE)

        # Calculate position and size
        padding = int(20 * min(self.scale_x, self.scale_y))
        margin = int(20 * min(self.scale_x, self.scale_y))
        toast_width = text.get_width() + padding * 2
        toast_height = text.get_height() + padding

        toast_x = (screen_width - toast_width) // 2
        toast_y = screen_height - toast_height - margin

        # Draw rounded rectangle background with error handling for SRCALPHA
        try:
            toast_surface = pygame.Surface((toast_width, toast_height), pygame.SRCALPHA)
            pygame.draw.rect(
                toast_surface,
                color,
                pygame.Rect(0, 0, toast_width, toast_height),
                border_radius=int(10 * min(self.scale_x, self.scale_y))
            )
        except Exception as e:
            print(f"Using fallback for toast surface: {e}")
            # Fallback to regular surface if SRCALPHA is not supported
            toast_surface = pygame.Surface((toast_width, toast_height))
            toast_surface.fill((color[0], color[1], color[2]))  # Use solid color without alpha
            # Try to draw rounded rectangle without alpha
            try:
                pygame.draw.rect(
                    toast_surface,
                    (color[0], color[1], color[2]),
                    pygame.Rect(0, 0, toast_width, toast_height),
                    border_radius=int(10 * min(self.scale_x, self.scale_y))
                )
            except:
                # If rounded rectangle fails, just use filled rectangle
                toast_surface.fill((color[0], color[1], color[2]))

        # Draw text
        text_x = (toast_width - text.get_width()) // 2
        text_y = (toast_height - text.get_height()) // 2
        toast_surface.blit(text, (text_x, text_y))

        # Cache the toast surface
        if not hasattr(self, '_toast_cache'):
            self._toast_cache = {}

        # Limit cache size
        if len(self._toast_cache) > 10:  # Keep only 10 most recent messages
            self._toast_cache.pop(next(iter(self._toast_cache)))

        self._toast_cache[self.message] = (toast_surface.copy(), toast_x, toast_y)

        # Draw to screen
        self.screen.blit(toast_surface, (toast_x, toast_y))

    def handle_mouse_click(self, pos):
        """Handle mouse click events"""
        # Check logout button click
        if hasattr(self, 'logout_button_rect') and self.logout_button_rect.collidepoint(pos):
            self.logout()
            return True

        # Check export button click
        if hasattr(self, 'export_button_rect') and self.export_button_rect.collidepoint(pos):
            if not self.export_in_progress:
                self.handle_export_click()
            return True

        # Check time period refresh button click
        if hasattr(self, 'time_period_refresh_rect') and self.time_period_refresh_rect.collidepoint(pos):
            print(f"Refreshing {self.current_time_period} data...")
            
            # Show a loading message
            self.show_message(f"Refreshing {self.current_time_period} data...", "info")
            
            # Refresh the data for the current time period
            if hasattr(self, 'stats_provider'):
                # Clear the cache for the current time period
                self.stats_provider.clear_cache()
                
                # Start a background thread to refresh the data
                
                def refresh_current_period_data():
                    try:
                        if self.current_time_period == "daily":
                            self.stats_provider.get_daily_earnings(datetime.now().strftime('%Y-%m-%d'))
                        elif self.current_time_period == "weekly":
                            self.stats_provider.get_weekly_stats()
                        elif self.current_time_period == "monthly":
                            self.stats_provider.get_monthly_stats()
                        elif self.current_time_period == "yearly":
                            self.stats_provider.get_yearly_stats()
                        print(f"{self.current_time_period.capitalize()} data refreshed")
                        
                        # Show success message
                        self.show_message(f"{self.current_time_period.capitalize()} data refreshed", "success")
                    except Exception as e:
                        print(f"Error refreshing {self.current_time_period} data: {e}")
                        self.show_message(f"Error refreshing data: {str(e)}", "error")
                
                # Start the thread
                refresh_thread = threading.Thread(target=refresh_current_period_data, daemon=True)
                refresh_thread.start()
            
            return True
            
        # Check time period tab clicks
        if hasattr(self, 'time_period_tab_rects'):
            for period, rect in self.time_period_tab_rects.items():
                if rect.collidepoint(pos):
                    if self.current_time_period != period:
                        self.current_time_period = period
                        print(f"Switched to {period} view")
                        
                        # Ensure data is loaded for monthly and yearly views
                        if period == "monthly" or period == "yearly":
                            if hasattr(self, 'stats_provider'):
                                # Start a background thread to refresh the data
                                
                                def refresh_period_data():
                                    try:
                                        print(f"Refreshing {period} data...")
                                        if period == "monthly":
                                            self.stats_provider.get_monthly_stats()
                                        else:  # yearly
                                            self.stats_provider.get_yearly_stats()
                                        print(f"{period.capitalize()} data refreshed")
                                    except Exception as e:
                                        print(f"Error refreshing {period} data: {e}")
                                
                                # Start the thread
                                refresh_thread = threading.Thread(target=refresh_period_data, daemon=True)
                                refresh_thread.start()
                        
                        return True

        # First check for search-related clicks
        if self.search_input_rect and self.search_input_rect.collidepoint(pos):
            # Clicked on search input - activate search
            self.search_active = True
            pygame.key.set_repeat(500, 50)  # Enable key repeat for text input
            return True

        if self.search_clear_rect and self.search_clear_rect.collidepoint(pos):
            # Clicked on clear search button
            self.clear_search()
            return True

        # If clicked outside search area, deactivate search
        if self.search_active:
            self.search_active = False
            pygame.key.set_repeat()  # Disable key repeat

        # Check for table row clicks
        if self.handle_table_mouse_click(pos):
            return True

        # Check for credit history table row clicks
        if self.credit_history_visible and self.handle_credit_table_mouse_click(pos):
            return True

        # Check for UUID copy clicks
        if "uuid_copy" in self.hit_areas and self.hit_areas["uuid_copy"].collidepoint(pos):
            self.copy_uuid_to_clipboard()
            return True

        if "uuid_copy_icon" in self.hit_areas and self.hit_areas["uuid_copy_icon"].collidepoint(pos):
            self.copy_uuid_to_clipboard()
            return True

        # Check for credit history toggle button click
        if "credit_history_toggle" in self.hit_areas and self.hit_areas["credit_history_toggle"].collidepoint(pos):
            # Play button click sound if available
            if self.button_click_sound:
                self.button_click_sound.play()

            # Toggle credit history visibility
            self.toggle_credit_history_visibility()
            return True

        # Check for refresh button click
        if "refresh_button" in self.hit_areas and self.hit_areas["refresh_button"].collidepoint(pos):
            # Play button click sound if available
            if self.button_click_sound:
                self.button_click_sound.play()

            # Set button click animation
            if "refresh_button" in self.button_states:
                self.button_states["refresh_button"]["click"] = True
                self.button_states["refresh_button"]["click_time"] = pygame.time.get_ticks()

            # Refresh the statistics data
            self.refresh_stats()
            return True

        # Check for recharge button click
        if hasattr(self, 'recharge_button') and self.recharge_button.collidepoint(pos):
            # Play button click sound if available
            if self.button_click_sound:
                self.button_click_sound.play()

            # Show recharge UI if available
            if hasattr(self, 'recharge_ui'):
                self.recharge_ui.show()
            else:
                self.show_message("Recharge functionality not available", "error")

            return True

        # Check for pagination controls
        if "prev_page" in self.hit_areas and self.hit_areas["prev_page"].collidepoint(pos):
            if self.history_page > 0:
                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Handle pagination
                self.handle_pagination("prev")
                self.show_message(f"Showing page {self.history_page + 1}", "info")
                return True

        if "next_page" in self.hit_areas and self.hit_areas["next_page"].collidepoint(pos):
            if self.history_page < self.total_history_pages - 1:
                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Handle pagination
                self.handle_pagination("next")
                self.show_message(f"Showing page {self.history_page + 1}", "info")
                return True

        # Check for direct interactions with navigation menu
        for key in self.hit_areas:
            if key.startswith("nav_") and self.hit_areas[key].collidepoint(pos):
                nav_item = key.replace("nav_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Set button click animation
                if key in self.button_states:
                    self.button_states[key]["click"] = True
                    self.button_states[key]["click_time"] = pygame.time.get_ticks()

                # Handle navigation
                if nav_item == "play":
                    # Return to main game screen
                    self.running = False
                    if self.on_close_callback:
                        self.on_close_callback()
                elif nav_item == "stats":
                    # Already on stats screen
                    self.show_message("Already on Stats screen", "info")
                elif nav_item == "settings":
                    # Go to settings screen with screen mode consistency
                    self.running = False
                    if self.on_close_callback:
                        self.on_close_callback()
                        # The main application will handle showing the settings page
                    else:
                        # Standalone mode - show settings page directly with screen mode consistency
                        try:
                            from settings_page import show_settings_page
                            # Ensure screen mode consistency before navigation
                            current_screen = self.screen_mode_manager.ensure_consistent_mode(self.screen)
                            show_settings_page(current_screen)
                        except Exception as e:
                            print(f"Error showing settings page: {e}")
                            self.show_message(f"Error loading settings page: {str(e)}", "error")
                elif nav_item == "help":
                    self.show_message("Help feature coming soon", "info")
                elif nav_item == "refresh":
                    # Refresh the statistics data
                    self.refresh_stats()
                    self.show_message("Statistics refreshed successfully", "success")

                return True

        return False

    def handle_mouse_wheel(self, wheel_y):
        """
        Handle mouse wheel scrolling for the stats page.

        Args:
            wheel_y: Mouse wheel direction (positive = up, negative = down)

        Returns:
            bool: True if scrolling occurred and redraw is needed
        """
        # Calculate scroll amount
        scroll_amount = wheel_y * self.scroll_speed

        # Update scroll position
        old_scroll_y = self.scroll_y
        self.scroll_y = max(0, min(self.max_scroll_y, self.scroll_y - scroll_amount))

        # Return True if scroll position changed
        return old_scroll_y != self.scroll_y

    def handle_mouse_motion(self, pos):
        """Handle mouse motion events"""
        # Handle table hover effects
        table_hover_changed = self.handle_table_mouse_motion(pos)

        # Update button hover states
        for btn_id, hit_area in self.hit_areas.items():
            hover = hit_area.collidepoint(pos)
            if btn_id in self.button_states:
                self.button_states[btn_id]["hover"] = hover

        # Return True if table hover state changed (to trigger redraw)
        return table_hover_changed

    def handle_pagination(self, direction):
        """FIXED: Handle pagination for game history with proper bounds checking"""
        if direction == "next" and self.history_page < self.total_history_pages - 1:
            self.history_page += 1
            print(f"DEBUG: Navigated to page {self.history_page + 1} of {self.total_history_pages}")
            # Load new page
            self.load_history_page()
            return True
        elif direction == "prev" and self.history_page > 0:
            self.history_page -= 1
            print(f"DEBUG: Navigated to page {self.history_page + 1} of {self.total_history_pages}")
            # Load new page
            self.load_history_page()
            return True
        else:
            print(f"DEBUG: Pagination blocked - current page: {self.history_page + 1}, total pages: {self.total_history_pages}")
        return False

    def _show_loading_indicator(self, message="Loading..."):
        """Show a loading indicator on the screen

        Args:
            message: The message to display
        """
        # Only show loading indicator if we're in standalone mode
        if not hasattr(self, 'integrated_mode') or not self.integrated_mode:
            try:
                # Get font and create text
                loading_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)
                loading_text = loading_font.render(message, True, WHITE)
                loading_rect = loading_text.get_rect(center=(self.screen.get_width()//2, self.screen.get_height()//2))

                # Create semi-transparent background
                bg_surface = pygame.Surface((self.screen.get_width(), self.screen.get_height()), pygame.SRCALPHA)
                bg_surface.fill((0, 0, 0, 180))  # Semi-transparent black

                # Display the loading indicator
                self.screen.blit(bg_surface, (0, 0))
                self.screen.blit(loading_text, loading_rect)
                pygame.display.flip()
            except Exception as e:
                print(f"Error showing loading indicator: {e}")

    def handle_refresh_event(self, event):
        """Handle refresh_stats events with deduplication"""
        import time

        current_time = time.time()
        event_source = getattr(event, 'source', 'unknown')

        # Check if we're in cooldown period
        if current_time - self._last_refresh_time < self._refresh_cooldown:
            print(f"REFRESH THROTTLED: Ignoring refresh event from {event_source} (cooldown active)")
            return False

        # Check if refresh is already in progress
        if self._refresh_in_progress:
            print(f"REFRESH THROTTLED: Ignoring refresh event from {event_source} (refresh in progress)")
            # Add to pending events for later processing
            self._pending_refresh_events.add(event_source)
            return False

        # Check if we already have a pending event from this source
        if event_source in self._pending_refresh_events:
            print(f"REFRESH THROTTLED: Ignoring duplicate refresh event from {event_source}")
            return False

        print(f"REFRESH ACCEPTED: Processing refresh event from {event_source}")

        # Process the refresh
        self._last_refresh_time = current_time
        self._pending_refresh_events.add(event_source)
        return self.refresh_stats()

    def refresh_stats(self):
        """PERFORMANCE FIX: Fast, non-blocking stats refresh without heavy operations"""
        # PERFORMANCE FIX: Check if refresh is already in progress
        if self._refresh_in_progress:
            print("REFRESH SKIPPED: Refresh already in progress")
            return False

        # PERFORMANCE FIX: Don't show loading indicator to prevent flickering
        self.show_message("Refreshing statistics...", "info")

        # PERFORMANCE FIX: Set flags briefly
        self._refresh_in_progress = True

        # PERFORMANCE FIX: Use fast refresh without heavy database operations
        refresh_thread = threading.Thread(target=self._refresh_stats_fast, daemon=True)
        refresh_thread.start()

        # PERFORMANCE FIX: Clear refresh flag quickly to prevent blocking
        pygame.time.set_timer(pygame.USEREVENT + 2, 500)  # Clear refresh flag after 500ms

        return True

    def _refresh_stats_background(self):
        """FIXED: Background thread to refresh stats with proper synchronization"""
        try:
            # FIXED: Step 1 - Sync database stats to ensure consistency
            print("Starting comprehensive stats refresh...")
            sync_success = self.sync_stats_data()
            if sync_success:
                print("Database stats synchronized successfully")
            else:
                print("Database sync failed, continuing with force refresh...")

            # Step 2 - Try to use force_refresh_data from stats_integration with timeout protection
            try:
                # Use timeout to prevent hanging
                import time

                refresh_done = threading.Event()
                refresh_success = [False]  # Use list to allow modification inside nested function

                def refresh_with_timeout():
                    try:
                        # Try stats_integration first
                        try:
                            from stats_integration import force_refresh_data
                            print("Forcing refresh of all stats data via stats_integration...")
                            result = force_refresh_data()
                            print(f"Force refresh result: {result}")
                            refresh_success[0] = result
                        except ImportError:
                            # Fall back to GameStatsIntegration
                            from game_stats_integration import GameStatsIntegration
                            result = GameStatsIntegration.force_refresh_data()
                            print(f"Direct force refresh result: {result}")
                            refresh_success[0] = result

                        refresh_done.set()
                    except Exception as e:
                        print(f"Error in refresh thread: {e}")
                        import traceback
                        traceback.print_exc()

                # Start refresh in separate thread
                timeout_thread = threading.Thread(target=refresh_with_timeout)
                timeout_thread.daemon = True
                timeout_thread.start()

                # Wait with timeout
                if not refresh_done.wait(timeout=3.0):  # 3 seconds timeout
                    print("WARNING: Data refresh operation timed out")

            except Exception as e:
                print(f"Error during data refresh: {e}")
                import traceback
                traceback.print_exc()

            # Clear any preloader cache (regardless of previous step success)
            try:
                from stats_preloader import get_stats_preloader
                preloader = get_stats_preloader()
                if hasattr(preloader, 'clear'):
                    clear_result = preloader.clear()
                    print(f"Cleared stats preloader cache: {clear_result}")
                elif hasattr(preloader, 'cache') and hasattr(preloader.cache, 'clear'):
                    preloader.cache.clear()
                    print("Cleared preloader cache directly")
            except ImportError:
                print("Stats preloader not available for clearing")
            except Exception as e:
                print(f"Error clearing preloader cache: {e}")

            # Try to integrate with game state if available (with timeout)
            if 'GAME_STATE_AVAILABLE' in globals() and GAME_STATE_AVAILABLE:
                try:
                    integrate_done = threading.Event()

                    def integrate_with_timeout():
                        try:
                            from game_state_handler import get_current_game_state
                            from game_stats_integration import integrate_with_game_state

                            # Get current game state
                            game_state = get_current_game_state()
                            if game_state:
                                # Integrate with game state
                                integrate_with_game_state(game_state)
                                print("Integrated with real-time game state")

                            integrate_done.set()
                        except Exception as e:
                            print(f"Error in integration thread: {e}")

                    # Start integration in separate thread
                    integrate_thread = threading.Thread(target=integrate_with_timeout)
                    integrate_thread.daemon = True
                    integrate_thread.start()

                    # Wait with timeout
                    if not integrate_done.wait(timeout=2.0):  # 2 seconds timeout
                        print("WARNING: Game state integration timed out")

                except Exception as e:
                    print(f"Error setting up game state integration: {e}")

            # Reload statistics in background
            self._load_statistics_background()
            
            # Also refresh all time period stats
            if hasattr(self, 'refresh_all_stats'):
                self.refresh_all_stats()

            # Reload game history (with timeout protection)
            try:
                history_done = threading.Event()

                def load_history_with_timeout():
                    try:
                        self.load_history_page()
                        history_done.set()
                    except Exception as e:
                        print(f"Error in history loading thread: {e}")

                # Start history loading in separate thread
                history_thread = threading.Thread(target=load_history_with_timeout)
                history_thread.daemon = True
                history_thread.start()

                # Wait with timeout
                if not history_done.wait(timeout=2.0):  # 2 seconds timeout
                    print("WARNING: Game history loading timed out")
            except Exception as e:
                print(f"Error setting up history loading: {e}")

            # Show success message
            self.show_message("Statistics refreshed successfully", "success")

            # Clear loading flag
            self.stats_loading_in_progress = False

        except Exception as e:
            print(f"Error in refresh_stats_background: {e}")
            import traceback
            traceback.print_exc()

            # Show error message
            self.show_message(f"Error refreshing statistics: {str(e)}", "error")

            # Clear loading flag
            self.stats_loading_in_progress = False
        finally:
            # Always clear refresh flags when done
            self._refresh_in_progress = False
            self._pending_refresh_events.clear()
            print("REFRESH COMPLETED: Cleared refresh flags")

    def handle_event(self, event):
        """Handle pygame events - this is a placeholder that can be overridden by integrations"""
        # Handle refresh_stats events in integrated mode
        if event.type == pygame.USEREVENT:
            if hasattr(event, 'stats_type') and event.stats_type == 'refresh_stats':
                return self.handle_refresh_event(event)

        # If we're in integrated mode, we need to handle events differently
        if self.integrated_mode and event.type == pygame.MOUSEBUTTONDOWN:
            # Check if we have a navigation click
            if self.handle_mouse_click(event.pos):
                return True
        return False

    @time_operation("load_history_page")
    def load_history_page(self):
        """FIXED: Load the current history page with proper pagination and error handling"""
        # Show loading indicator
        self.show_message("Loading history...", "info")

        print(f"DEBUG: Loading history page {self.history_page + 1} of {self.total_history_pages} (page_size: {self.history_page_size})")

        # Clear any cached data to force fresh load
        if hasattr(self, 'game_history'):
            print(f"DEBUG: Previous game_history had {len(self.game_history) if self.game_history else 0} records")

        # FIXED: Use consistent page size without arbitrary increases
        effective_page_size = self.history_page_size
        print(f"DEBUG: Using page size: {effective_page_size}")

        # Try to use preloader first
        try:
            from stats_preloader import get_stats_preloader
            preloader = get_stats_preloader()

            # FIXED: Get game history from preloader with proper page parameters
            result = preloader.get_game_history_page(
                self.history_page, effective_page_size)

            # Validate the result to prevent unpacking errors
            if result and isinstance(result, (tuple, list)) and len(result) >= 2:
                history, total_pages = result[0], result[1]
                if history:
                    # Store original data for search functionality
                    self.original_game_history = history.copy()
                    self.game_history = history
                    # Store all game history for accurate remainder calculation
                    self.all_game_history = history.copy()
                    self.total_history_pages = total_pages
            else:
                print(f"Warning: preloader.get_game_history_page returned unexpected result: {result}")
                self.game_history = []
                self.total_history_pages = 1

                # Log performance if monitoring is available
                if PERFORMANCE_MONITORING_AVAILABLE:
                    performance_monitor.metrics.setdefault('cache_hits', 0)
                    performance_monitor.metrics['cache_hits'] += 1

                return
        except ImportError:
            pass  # Fall back to direct database access
        except Exception as e:
            print(f"Error using preloader for history: {e}")

        # Fall back to direct database access
        if STATS_DB_AVAILABLE:
            try:
                # Log performance if monitoring is available
                if PERFORMANCE_MONITORING_AVAILABLE:
                    performance_monitor.metrics.setdefault('cache_misses', 0)
                    performance_monitor.metrics['cache_misses'] += 1

                # Get game history with page and effective page_size parameters
                result = get_game_history(page=self.history_page, page_size=effective_page_size)
                print(f"DEBUG: get_game_history returned: {type(result)} with length {len(result) if hasattr(result, '__len__') else 'N/A'}")

                # Validate the result to prevent unpacking errors
                if result and isinstance(result, (tuple, list)) and len(result) >= 2:
                    history, total_pages = result[0], result[1]
                    print(f"DEBUG: Unpacked - history: {len(history) if history else 0} records, total_pages: {total_pages}")

                    # Store original data for search functionality
                    self.original_game_history = history.copy() if history else []
                    self.game_history, self.total_history_pages = history, total_pages

                    # Log the actual data we got
                    if history:
                        print(f"DEBUG: First record: {history[0].get('username', 'Unknown')} - {history[0].get('date_time', 'No date')}")
                        if len(history) > 1:
                            print(f"DEBUG: Last record: {history[-1].get('username', 'Unknown')} - {history[-1].get('date_time', 'No date')}")
                        print(f"DEBUG: All usernames: {[record.get('username', 'Unknown') for record in history]}")
                else:
                    print(f"Warning: get_game_history returned unexpected result: {result}")
                    self.game_history = []
                    self.total_history_pages = 1
            except Exception as e:
                print(f"Error loading game history page: {e}")
                self.show_message(f"Error loading history: {str(e)}", "error")

    def initialize_stats_provider(self):
        """Initialize the stats provider and pre-load data in the background."""
        if not hasattr(self, 'stats_provider'):
            if EMERGENCY_STATS_AVAILABLE:
                self.stats_provider = get_emergency_stats_provider()
                print("Using EMERGENCY ultra-fast stats provider")
            elif PERFORMANCE_STATS_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif EMERGENCY_STATS_AVAILABLE:
                self.stats_provider = get_emergency_stats_provider()
                print("Using EMERGENCY ultra-fast stats provider")
            elif PERFORMANCE_STATS_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif PERFORMANCE_OPTIMIZED_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif SIMPLE_STATS_AVAILABLE:
                self.stats_provider = get_simple_stats_provider()
                print("Using simple stats provider")
            else:
                self.stats_provider = CentralizedStatsProvider()
                print("Using original stats provider")
        
        # Start a background thread to pre-load monthly and yearly stats
        
        def preload_stats_data():
            try:
                print("Pre-loading monthly and yearly stats data...")
                # This will trigger data loading and caching
                self.stats_provider.get_monthly_stats()
                self.stats_provider.get_yearly_stats()
                print("Monthly and yearly stats pre-loading complete")
            except Exception as e:
                print(f"Error pre-loading stats data: {e}")
        
        # Start the thread
        preload_thread = threading.Thread(target=preload_stats_data, daemon=True)
        preload_thread.start()
        
    def refresh_all_stats(self):
        """Refresh all stats data (daily, weekly, monthly, yearly)."""
        if not hasattr(self, 'stats_provider'):
            if EMERGENCY_STATS_AVAILABLE:
                self.stats_provider = get_emergency_stats_provider()
                print("Using EMERGENCY ultra-fast stats provider")
            elif PERFORMANCE_STATS_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif EMERGENCY_STATS_AVAILABLE:
                self.stats_provider = get_emergency_stats_provider()
                print("Using EMERGENCY ultra-fast stats provider")
            elif PERFORMANCE_STATS_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif PERFORMANCE_OPTIMIZED_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif SIMPLE_STATS_AVAILABLE:
                self.stats_provider = get_simple_stats_provider()
                print("Using simple stats provider")
            else:
                self.stats_provider = CentralizedStatsProvider()
                print("Using original stats provider")
            
        # Clear the cache to force a refresh
        self.stats_provider.clear_cache()
        
        # Start a background thread to refresh all stats
        
        def refresh_all_stats_data():
            try:
                print("Refreshing all stats data...")
                # This will trigger data loading and caching for all time periods
                self.stats_provider.get_daily_earnings(datetime.now().strftime('%Y-%m-%d'))
                self.stats_provider.get_weekly_stats()
                self.stats_provider.get_monthly_stats()
                self.stats_provider.get_yearly_stats()
                print("All stats data refreshed")
            except Exception as e:
                print(f"Error refreshing all stats data: {e}")
        
        # Start the thread
        refresh_thread = threading.Thread(target=refresh_all_stats_data, daemon=True)
        refresh_thread.start()

    @time_operation("load_statistics")
    def load_statistics(self):
        """PERFORMANCE FIX: Fast, non-blocking statistics loading with immediate UI response"""
        # PERFORMANCE FIX: Prevent concurrent loading with immediate return
        if hasattr(self, 'stats_loading_in_progress') and self.stats_loading_in_progress:
            print("LOAD SKIPPED: Statistics loading already in progress")
            return

        # PERFORMANCE FIX: Initialize with cached values immediately for instant UI response
        self._initialize_immediate_data()

        # PERFORMANCE FIX: Use single, optimized stats provider
        self._initialize_optimized_stats_provider()

        # PERFORMANCE FIX: Set loading flag briefly, then disable to prevent flickering
        self.stats_loading_in_progress = True

        # PERFORMANCE FIX: Load data asynchronously without blocking UI
        loading_thread = threading.Thread(target=self._load_statistics_fast_background, daemon=True)
        loading_thread.start()

        # PERFORMANCE FIX: Immediately mark loading as complete to prevent UI blocking
        # Background thread will update data when ready
        pygame.time.set_timer(pygame.USEREVENT + 1, 100)  # Clear loading flag after 100ms

    def _initialize_immediate_data(self):
        """PERFORMANCE FIX: Initialize with cached or default data for immediate UI response"""
        # Initialize with default values for immediate display
        self.stats_data = {
            "games_played": 0,
            "total_winners": 0,
            "total_prize_pool": 0,
            "player_count": 0,
            "average_game_duration": 0,
            "top_players": [],
            "number_frequencies": {},
            "session_start_time": time.time(),
            "recent_activity": []
        }

        # PERFORMANCE FIX: Try to load from cache first for instant display
        try:
            cache_file = os.path.join('data', 'stats_cache_instant.json')
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cached_data = json.load(f)
                    self.weekly_stats = cached_data.get('weekly_stats', [])
                    self.total_earnings = cached_data.get('total_earnings', 0)
                    self.daily_earnings = cached_data.get('daily_earnings', 0)
                    self.daily_games = cached_data.get('daily_games', 0)
                    self.game_history = cached_data.get('game_history', [])[:10]  # Only first 10 for speed
                    print("PERFORMANCE: Loaded cached data for instant display")
                    return
        except Exception as e:
            print(f"Cache load failed: {e}")

        # Fallback to minimal default data
        self._load_minimal_data()

    def _initialize_optimized_stats_provider(self):
        """PERFORMANCE FIX: Use single, most efficient stats provider"""
        if not hasattr(self, 'stats_provider'):
            # PERFORMANCE FIX: Use only the fastest available provider
            if EMERGENCY_STATS_AVAILABLE:
                self.stats_provider = get_emergency_stats_provider()
                print("PERFORMANCE: Using EMERGENCY ultra-fast stats provider")
            elif PERFORMANCE_OPTIMIZED_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("PERFORMANCE: Using performance-optimized stats provider")
            else:
                # PERFORMANCE FIX: Use lightweight provider instead of full CentralizedStatsProvider
                self.stats_provider = self._create_lightweight_provider()
                print("PERFORMANCE: Using lightweight stats provider")

    def _create_lightweight_provider(self):
        """PERFORMANCE FIX: Create a lightweight stats provider for fast loading"""
        class LightweightStatsProvider:
            def __init__(self):
                self.cache = {}

            def get_daily_earnings(self, date_str):
                return self.cache.get(f'daily_earnings_{date_str}', 0)

            def get_weekly_stats(self):
                return self.cache.get('weekly_stats', [])

            def clear_cache(self):
                self.cache.clear()

        return LightweightStatsProvider()

    def _load_minimal_data(self):
        """PERFORMANCE FIX: Load minimal data needed for initial display"""
        # Use default empty data initially
        today = datetime.now()
        self.weekly_stats = []
        for i in range(7):
            day = today - timedelta(days=6-i)
            self.weekly_stats.append({
                'date': day.strftime('%Y-%m-%d'),
                'games_played': 0,
                'earnings': 0,
                'winners': 0,
                'total_players': 0
            })

        # Set default summary values
        self.total_earnings = 0
        self.daily_earnings = 0
        self.daily_games = 0
        self.wallet_balance = self.get_current_wallet_balance()  # Get real wallet balance
        self.game_history = []
        self.total_history_pages = 1

    def _load_statistics_fast_background(self):
        """PERFORMANCE FIX: Fast background loading with aggressive timeouts"""
        try:
            print("PERFORMANCE: Starting fast background data loading")
            start_time = time.time()

            # PERFORMANCE FIX: Use very short timeout to prevent blocking
            max_load_time = 2.0  # Maximum 2 seconds for all operations

            # PERFORMANCE FIX: Try cached data first
            if self._try_load_from_cache():
                print(f"PERFORMANCE: Loaded from cache in {time.time() - start_time:.2f}s")
                self.stats_loading_in_progress = False
                return

            # PERFORMANCE FIX: Quick database load with timeout
            if time.time() - start_time < max_load_time:
                self._quick_database_load(max_load_time - (time.time() - start_time))

            # PERFORMANCE FIX: Save to cache for next time
            self._save_to_cache()

            elapsed = time.time() - start_time
            print(f"PERFORMANCE: Background loading completed in {elapsed:.2f}s")

        except Exception as e:
            print(f"PERFORMANCE: Background loading error: {e}")
        finally:
            # PERFORMANCE FIX: Always clear loading flag
            self.stats_loading_in_progress = False

    def _try_load_from_cache(self):
        """PERFORMANCE FIX: Try to load from various cache sources"""
        try:
            # Try preloader cache first
            if PRELOADER_AVAILABLE:
                from stats_preloader import get_stats_preloader
                preloader = get_stats_preloader()
                cached_weekly = preloader.get_cached_data('weekly_stats')
                if cached_weekly:
                    self.weekly_stats = cached_weekly
                    self.total_earnings = preloader.get_cached_data('total_earnings', 0)
                    self.daily_earnings = preloader.get_cached_data('daily_earnings', 0)
                    self.daily_games = preloader.get_cached_data('daily_games', 0)
                    return True

            # Try instant cache file
            cache_file = os.path.join('data', 'stats_cache_instant.json')
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cached_data = json.load(f)
                    # Check if cache is recent (less than 5 minutes old)
                    cache_time = cached_data.get('timestamp', 0)
                    if time.time() - cache_time < 300:  # 5 minutes
                        self.weekly_stats = cached_data.get('weekly_stats', [])
                        self.total_earnings = cached_data.get('total_earnings', 0)
                        self.daily_earnings = cached_data.get('daily_earnings', 0)
                        self.daily_games = cached_data.get('daily_games', 0)
                        self.game_history = cached_data.get('game_history', [])
                        return True

        except Exception as e:
            print(f"Cache load error: {e}")

        return False

    def _quick_database_load(self, timeout):
        """PERFORMANCE FIX: Quick database load with strict timeout"""
        try:
            if not STATS_DB_AVAILABLE:
                return

            start_time = time.time()

            # PERFORMANCE FIX: Use get_stats_summary with timeout
            if time.time() - start_time < timeout:
                stats_summary = get_stats_summary()
                if stats_summary:
                    self.weekly_stats = stats_summary.get("weekly_stats", self.weekly_stats)
                    self.total_earnings = stats_summary.get("total_earnings", self.total_earnings)
                    self.daily_earnings = stats_summary.get("daily_earnings", self.daily_earnings)
                    self.daily_games = stats_summary.get("daily_games", self.daily_games)
                    self.wallet_balance = stats_summary.get("wallet_balance", self.wallet_balance)

            # PERFORMANCE FIX: Load only first page of history with timeout
            if time.time() - start_time < timeout:
                result = get_game_history(page=0, page_size=10)
                if result and len(result) >= 2:
                    history, total_pages = result[0], result[1]
                    if history:
                        self.game_history = history
                        self.total_history_pages = total_pages

        except Exception as e:
            print(f"Quick database load error: {e}")

    def _save_to_cache(self):
        """PERFORMANCE FIX: Save current data to cache for next load"""
        try:
            cache_data = {
                'weekly_stats': self.weekly_stats,
                'total_earnings': self.total_earnings,
                'daily_earnings': self.daily_earnings,
                'daily_games': self.daily_games,
                'game_history': self.game_history[:10],  # Only cache first 10 records
                'timestamp': time.time()
            }

            cache_file = os.path.join('data', 'stats_cache_instant.json')
            os.makedirs(os.path.dirname(cache_file), exist_ok=True)

            with open(cache_file, 'w') as f:
                json.dump(cache_data, f)

        except Exception as e:
            print(f"Cache save error: {e}")

    def _load_statistics_background(self):
        """Background thread to load statistics without blocking the UI"""
        try:
            # Check if loading is already in progress (double-check in background thread)
            if not hasattr(self, 'stats_loading_in_progress') or not self.stats_loading_in_progress:
                print("BACKGROUND LOAD SKIPPED: Loading not in progress")
                return

            print("BACKGROUND LOAD STARTED: Loading statistics data")

            # Try to load from stats preloader first (fastest)
            try:
                from stats_preloader import get_stats_preloader
                preloader = get_stats_preloader()

                # Use a timeout to prevent hanging
                preloader_timeout = 2.0  # 2 seconds timeout
                start_time = time.time()

                # Start preloading in background if not already running
                preloader.start_preloading()

                # Get cached data with timeout check
                if time.time() - start_time < preloader_timeout:
                    self.weekly_stats = preloader.get_cached_data('weekly_stats', self.weekly_stats)
                    self.total_earnings = preloader.get_cached_data('total_earnings', self.total_earnings)
                    self.daily_earnings = preloader.get_cached_data('daily_earnings', self.daily_earnings)
                    self.daily_games = preloader.get_cached_data('daily_games', self.daily_games)
                    self.wallet_balance = preloader.get_cached_data('wallet_balance', self.wallet_balance)
                    
                    # Also load monthly and yearly stats from the centralized provider
                    if hasattr(self, 'stats_provider'):
                        # Force refresh the stats provider cache
                        self.stats_provider.clear_cache()
                        # This will trigger data loading in the background

                    # Get game history
                    result = preloader.get_game_history_page(0, self.history_page_size)

                    # Validate the result to prevent unpacking errors
                    if result and isinstance(result, (tuple, list)) and len(result) >= 2:
                        history, total_pages = result[0], result[1]
                        if history:
                            self.game_history = history
                            # Store all game history for accurate remainder calculation
                            self.all_game_history = history.copy()
                            self.total_history_pages = total_pages
                    else:
                        print(f"Warning: preloader.get_game_history_page returned unexpected result in background: {result}")
                        self.game_history = []
                        self.total_history_pages = 1

                    # If we got data from the preloader, we're done
                    if self.weekly_stats:
                        print("Statistics loaded from preloader cache")
                        self.stats_loading_in_progress = False
                        return

                print("Preloader cache not fully populated or timed out, falling back to database")
            except ImportError:
                print("Stats preloader not available, falling back to database")
            except Exception as e:
                print(f"Error using stats preloader: {e}")
                # Fall back to database if preloader fails

            # Try to load from database if preloader failed or is not available
            if STATS_DB_AVAILABLE:
                try:
                    # Connection to real database will be made here
                    # Use a timeout to prevent hanging
                    db_timeout = 3.0  # 3 seconds timeout
                    start_time = time.time()

                    # Get stats summary from database
                    if time.time() - start_time < db_timeout:
                        stats_summary = get_stats_summary()

                        # Update stats data with database values
                        if stats_summary and isinstance(stats_summary, dict):
                            self.weekly_stats = stats_summary.get("weekly_stats", self.weekly_stats)
                            self.total_earnings = stats_summary.get("total_earnings", self.total_earnings)
                            self.daily_earnings = stats_summary.get("daily_earnings", self.daily_earnings)
                            self.daily_games = stats_summary.get("daily_games", self.daily_games)
                            self.wallet_balance = stats_summary.get("wallet_balance", self.wallet_balance)
                            
                            # Also load monthly and yearly stats from the centralized provider
                            if hasattr(self, 'stats_provider'):
                                # Force refresh the stats provider cache
                                self.stats_provider.clear_cache()
                                # This will trigger data loading in the background

                    # Get game history with page and page_size parameters (with timeout)
                    if time.time() - start_time < db_timeout:
                        print("DEBUG: Attempting to load game history from database...")
                        result = get_game_history(page=0, page_size=self.history_page_size)
                        print(f"DEBUG: get_game_history returned: {type(result)} with length: {len(result) if result else 'None'}")

                        # Validate the result to prevent unpacking errors
                        if result and isinstance(result, (tuple, list)) and len(result) >= 2:
                            history, total_pages = result[0], result[1]
                            print(f"DEBUG: Unpacked history length: {len(history) if history else 'None'}, total_pages: {total_pages}")
                            if history:
                                self.game_history = history
                                # Store all game history for accurate remainder calculation
                                self.all_game_history = history.copy()
                                self.total_history_pages = total_pages
                                print(f"DEBUG: Successfully loaded {len(self.game_history)} game history records")
                            else:
                                print("DEBUG: History is empty or None - generating sample data for testing")
                                self.game_history = self._generate_sample_game_history()
                                self.total_history_pages = 1
                        else:
                            print(f"Warning: get_game_history returned unexpected result in background loading: {result}")
                            print("DEBUG: Generating sample data due to unexpected result")
                            self.game_history = self._generate_sample_game_history()
                            self.total_history_pages = 1

                    print("Statistics loaded from database")
                    self.stats_loading_in_progress = False
                    return
                except Exception as e:
                    print(f"Error loading statistics from database: {str(e)}")
                    # Fall back to JSON if database fails
                    # Fall back to JSON if database fails

                # Fallback: Load stats from data/stats.json if available
                stats_path = os.path.join('data', 'stats.json')
                if os.path.exists(stats_path):
                    try:
                        with open(stats_path, 'r') as f:
                            loaded_stats = json.load(f)
                            # Update stats_data with loaded values
                            for key, value in loaded_stats.items():
                                if key in self.stats_data:
                                    self.stats_data[key] = value
                        print(f"Statistics loaded from {stats_path}")
                    except Exception as e:
                        print(f"Error loading statistics from {stats_path}: {str(e)}")
                        # Create the stats file if it doesn't exist or is invalid
                        self.save_statistics()
                else:
                    print(f"Statistics file {stats_path} not found, creating new file")
                    # Create the stats file
                    self.save_statistics()

                # Load players data to get current player count
                try:
                    self.players = load_players_from_json()
                    if self.players:
                        # Update player count in stats
                        self.stats_data["player_count"] = max(self.stats_data["player_count"], len(self.players))
                except Exception as e:
                    print(f"Error loading players data: {str(e)}")

                # Final fallback for weekly stats if not loaded yet
                if not self.weekly_stats:
                    try:
                        if STATS_DB_AVAILABLE:
                            # Use a timeout to prevent hanging
                            weekly_timeout = 2.0  # 2 seconds timeout
                            start_time = time.time()

                            if time.time() - start_time < weekly_timeout:
                                try:
                                    from stats_integration import get_weekly_stats
                                    weekly_stats = get_weekly_stats()
                                    if weekly_stats:
                                        self.weekly_stats = weekly_stats
                                        print("Retrieved real weekly stats from database")

                                        # Calculate real summary values
                                        self.total_earnings = sum(day.get('earnings', 0) for day in self.weekly_stats)
                                        # Get today's earnings
                                        today_date = datetime.now().strftime('%Y-%m-%d')
                                        today_stats = next((day for day in self.weekly_stats if day.get('date') == today_date), None)
                                        self.daily_earnings = today_stats.get('earnings', 0) if today_stats else 0
                                        self.daily_games = today_stats.get('games_played', 0) if today_stats else 0

                                        # Get real wallet balance
                                        self.wallet_balance = self.get_current_wallet_balance()
                                except Exception as weekly_e:
                                    print(f"Error getting weekly stats: {weekly_e}")
                    except Exception as e:
                        print(f"Error retrieving real weekly stats: {e}")

                # Final fallback for game history if not loaded yet
                if not self.game_history:
                    try:
                        if STATS_DB_AVAILABLE:
                            # Use a timeout to prevent hanging
                            history_timeout = 2.0  # 2 seconds timeout
                            start_time = time.time()

                            if time.time() - start_time < history_timeout:
                                try:
                                    result = get_game_history(page=0, page_size=self.history_page_size)

                                    # Validate the result to prevent unpacking errors
                                    if result and isinstance(result, (tuple, list)) and len(result) >= 2:
                                        history, total_pages = result[0], result[1]
                                        if history:
                                            self.game_history = history
                                            # Store all game history for accurate remainder calculation
                                            self.all_game_history = history.copy()
                                            self.total_history_pages = total_pages
                                            print(f"Retrieved {len(self.game_history)} real game history records")
                                    else:
                                        print(f"Warning: get_game_history returned unexpected result in fallback loading: {result}")
                                        self.game_history = []
                                        self.total_history_pages = 1
                                except Exception as history_e:
                                    print(f"Error getting game history: {history_e}")
                    except Exception as e:
                        print(f"Error retrieving real game history: {e}")

                # Mark loading as complete
                self.stats_loading_in_progress = False
                print("BACKGROUND LOAD COMPLETED: Statistics data loaded successfully")

        except Exception as e:
            print(f"Background loading error: {e}")
            import traceback
            traceback.print_exc()
            # Mark loading as complete even on error
            self.stats_loading_in_progress = False
            self.players = []
            print("BACKGROUND LOAD COMPLETED: Statistics data loading failed")

        # Load session data if available to get current prize pool
        try:
            session_path = os.path.join('data', 'current_session.json')
            if os.path.exists(session_path):
                with open(session_path, 'r') as f:
                    session_data = json.load(f)

                session_info = session_data.get("session_info", {})
                current_prize_pool = session_info.get("prize_pool_etb", 0)

                # Update total prize pool if current is higher
                self.stats_data["total_prize_pool"] = max(self.stats_data["total_prize_pool"], current_prize_pool)
        except Exception as e:
            print(f"Error loading session data: {str(e)}")

        # If no data is available from database, attempt to connect directly to game state
        if self.stats_data["games_played"] == 0 and not os.path.exists(stats_path):
            print("No statistics data available. Attempting to connect to game state...")
            try:
                # Try to import game state handler for real-time data
                from game_state_handler import get_current_game_state
                game_state = get_current_game_state()

                if game_state:
                    # Extract real data from game state
                    self.stats_data["games_played"] = game_state.get("total_games_played", 1)
                    self.stats_data["total_winners"] = game_state.get("total_winners", 0)
                    self.stats_data["average_game_duration"] = game_state.get("average_duration", 900)  # 15 minutes default
                    self.stats_data["session_start_time"] = game_state.get("session_start_time", time.time() - 3600)

                    # Get actual number frequencies from called numbers history
                    number_frequencies = game_state.get("number_frequencies", {})
                    if number_frequencies:
                        self.stats_data["number_frequencies"] = number_frequencies
                    else:
                        # Initialize with zeros instead of random values
                        for i in range(1, 76):
                            self.stats_data["number_frequencies"][i] = 0

                    # Get actual activity log
                    activity_log = game_state.get("activity_log", [])
                    if activity_log:
                        self.stats_data["recent_activity"] = activity_log

                    print("Successfully loaded real data from game state")
                else:
                    # If no game state, initialize with zeros
                    self.stats_data["games_played"] = 0
                    self.stats_data["total_winners"] = 0
                    self.stats_data["average_game_duration"] = 0

                    for i in range(1, 76):
                        self.stats_data["number_frequencies"][i] = 0

                    self.stats_data["recent_activity"] = []
                    print("No game state available. Initialized with empty data.")
            except ImportError:
                print("Game state handler not available. Using minimal initialization.")
                # Initialize with empty/zero values rather than fake data
                self.stats_data["games_played"] = 0
                self.stats_data["total_winners"] = 0
                self.stats_data["average_game_duration"] = 0

                for i in range(1, 76):
                    self.stats_data["number_frequencies"][i] = 0

                self.stats_data["recent_activity"] = []
            except Exception as e:
                print(f"Error connecting to game state: {e}")
                # Initialize with minimal data - no fake values
                self.stats_data["games_played"] = 0
                self.stats_data["total_winners"] = 0

            # Save the initialized data
            self.save_statistics()

            # Create sample weekly stats if not loaded from database
            if not self.weekly_stats:
                # Try to get real weekly stats from database with timeout
                try:
                    if STATS_DB_AVAILABLE:
                        from stats_integration import get_weekly_stats

                        # Use a timeout to prevent hanging
                        weekly_timeout = 2.0  # 2 seconds timeout
                        start_time = time.time()

                        if time.time() - start_time < weekly_timeout:
                            weekly_stats = get_weekly_stats()
                            if weekly_stats:
                                self.weekly_stats = weekly_stats
                                print("Retrieved real weekly stats from database")

                                # Calculate real summary values
                                self.total_earnings = sum(day.get('earnings', 0) for day in self.weekly_stats)
                                # Get today's earnings
                                today_date = datetime.now().strftime('%Y-%m-%d')
                                today_stats = next((day for day in self.weekly_stats if day.get('date') == today_date), None)
                                self.daily_earnings = today_stats.get('earnings', 0) if today_stats else 0
                                self.daily_games = today_stats.get('games_played', 0) if today_stats else 0

                                # Get real wallet balance
                                self.wallet_balance = self.get_current_wallet_balance()
                except Exception as e:
                    print(f"Error retrieving real weekly stats: {e}")

            # Final fallback for game history if not loaded yet
            if not self.game_history:
                try:
                    if STATS_DB_AVAILABLE:
                        # Use a timeout to prevent hanging
                        history_timeout = 2.0  # 2 seconds timeout
                        start_time = time.time()

                        if time.time() - start_time < history_timeout:
                            result = get_game_history(page=0, page_size=self.history_page_size)

                            # Validate the result to prevent unpacking errors
                            if result and isinstance(result, (tuple, list)) and len(result) >= 2:
                                history, total_pages = result[0], result[1]
                                if history:
                                    self.game_history = history
                                    # Store all game history for accurate remainder calculation
                                    self.all_game_history = history.copy()
                                    self.total_history_pages = total_pages
                                    print(f"Retrieved {len(self.game_history)} real game history records")
                            else:
                                print(f"Warning: get_game_history returned unexpected result in final fallback: {result}")
                                self.game_history = []
                                self.total_history_pages = 1
                except Exception as e:
                    print(f"Error retrieving real game history: {e}")

            # Mark loading as complete
            self.stats_loading_in_progress = False

    def save_statistics(self):
        """Save statistics data to data/stats.json"""
        stats_path = os.path.join('data', 'stats.json')
        try:
            # Ensure the data directory exists
            os.makedirs(os.path.dirname(stats_path), exist_ok=True)

            # Save the stats data to file
            with open(stats_path, 'w') as f:
                json.dump(self.stats_data, f, indent=4)
            print(f"Statistics saved to {stats_path}")
        except Exception as e:
            print(f"Error saving statistics to {stats_path}: {str(e)}")

    @staticmethod
    def update_game_statistics(game_data):
        """Update statistics when a game is completed

        Args:
            game_data: Dictionary containing game statistics
                - winner_name: Name of the winner
                - winner_cartella: Cartella number of the winner
                - claim_type: Type of claim (e.g., 'Full House', 'First Line')
                - game_duration: Duration of the game in seconds
                - player_count: Number of players in the game
                - prize_amount: Prize amount for this game
                - commission_percentage: Commission percentage for this game
                - called_numbers: List of numbers called during the game
                - is_demo_mode: Boolean indicating if the game was in demo mode
        """
        # Check if this was a demo mode game
        is_demo_mode = game_data.get("is_demo_mode", False)

        # If in demo mode, don't update statistics
        if is_demo_mode:
            print("Game was in demo mode - statistics not updated")
            return

        # Try to use the database first
        if STATS_DB_AVAILABLE:
            try:
                from stats_integration import record_game_completed
                record_game_completed(game_data)
                print("Game statistics recorded in database")
                return
            except Exception as e:
                print(f"Error recording game statistics in database: {e}")
                print("Falling back to JSON storage")

        # Fallback: Use JSON storage
        # Load current statistics
        stats_path = os.path.join('data', 'stats.json')
        stats_data = {
            "games_played": 0,
            "total_winners": 0,
            "total_prize_pool": 0,
            "player_count": 0,
            "average_game_duration": 0,
            "top_players": [],
            "number_frequencies": {},
            "session_start_time": time.time(),
            "recent_activity": []
        }

        # Load existing stats if available
        if os.path.exists(stats_path):
            try:
                with open(stats_path, 'r') as f:
                    loaded_stats = json.load(f)
                    # Update stats_data with loaded values
                    for key, value in loaded_stats.items():
                        if key in stats_data:
                            stats_data[key] = value
            except Exception as e:
                print(f"Error loading statistics for update: {str(e)}")

        # Update statistics with new game data
        stats_data["games_played"] += 1
        stats_data["total_winners"] += 1

        # Update prize pool
        if "prize_amount" in game_data and game_data["prize_amount"] > 0:
            stats_data["total_prize_pool"] += game_data["prize_amount"]

        # Update player count
        if "player_count" in game_data and game_data["player_count"] > 0:
            stats_data["player_count"] = max(stats_data["player_count"], game_data["player_count"])

        # Update average game duration
        if "game_duration" in game_data and game_data["game_duration"] > 0:
            current_avg = stats_data["average_game_duration"]
            games_played = stats_data["games_played"]

            if games_played > 1:
                # Calculate new average
                total_duration = current_avg * (games_played - 1)
                total_duration += game_data["game_duration"]
                stats_data["average_game_duration"] = total_duration / games_played
            else:
                # First game, just use its duration
                stats_data["average_game_duration"] = game_data["game_duration"]

        # Update number frequencies
        if "called_numbers" in game_data and game_data["called_numbers"]:
            number_freq = stats_data["number_frequencies"]
            for num in game_data["called_numbers"]:
                if str(num) in number_freq:
                    number_freq[str(num)] += 1
                else:
                    number_freq[str(num)] = 1
            stats_data["number_frequencies"] = number_freq

        # Add to recent activity
        if "winner_name" in game_data and "claim_type" in game_data:
            # Format current time
            current_time = time.strftime("%H:%M", time.localtime())

            # Create activity entry
            activity = {
                "time": current_time,
                "event": f"Player '{game_data['winner_name']}' won with {game_data['claim_type']}"
            }

            # Add to recent activity (keep most recent 10)
            recent_activity = stats_data.get("recent_activity", [])
            recent_activity.insert(0, activity)  # Add at beginning
            stats_data["recent_activity"] = recent_activity[:10]  # Keep only most recent 10

        # Save updated statistics
        try:
            # Ensure the data directory exists
            os.makedirs(os.path.dirname(stats_path), exist_ok=True)

            # Save the stats data to file
            with open(stats_path, 'w') as f:
                json.dump(stats_data, f, indent=4)
            print(f"Game statistics updated in {stats_path}")
        except Exception as e:
            print(f"Error saving updated statistics: {str(e)}")

    @classmethod
    def add_game_start_event(cls, player_count, bet_amount=50, is_demo_mode=False):
        """Add a game start event to the activity log

        Args:
            player_count: Number of players in the game
            bet_amount: Bet amount per player
            is_demo_mode: Boolean indicating if the game is in demo mode
        """
        # If in demo mode, don't update statistics
        if is_demo_mode:
            print("Game started in demo mode - statistics not updated")
            return

        # Try to use the database first
        if STATS_DB_AVAILABLE:
            try:
                from stats_integration import record_game_started
                record_game_started(player_count, bet_amount, is_demo_mode)
                print("Game start event recorded in database")
                return
            except Exception as e:
                print(f"Error recording game start event in database: {e}")
                print("Falling back to JSON storage")

        # Fallback: Use JSON storage
        # Load current statistics
        stats_path = os.path.join('data', 'stats.json')
        stats_data = {
            "games_played": 0,
            "total_winners": 0,
            "total_prize_pool": 0,
            "player_count": 0,
            "average_game_duration": 0,
            "top_players": [],
            "number_frequencies": {},
            "session_start_time": time.time(),
            "recent_activity": []
        }

        # Load existing stats if available
        if os.path.exists(stats_path):
            try:
                with open(stats_path, 'r') as f:
                    loaded_stats = json.load(f)
                    # Update stats_data with loaded values
                    for key, value in loaded_stats.items():
                        if key in stats_data:
                            stats_data[key] = value
            except Exception as e:
                print(f"Error loading statistics for update: {str(e)}")

        # Format current time
        current_time = time.strftime("%H:%M", time.localtime())

        # Create activity entry
        activity = {
            "time": current_time,
            "event": f"Game started with {player_count} players"
        }

        # Add to recent activity (keep most recent 10)
        recent_activity = stats_data.get("recent_activity", [])
        recent_activity.insert(0, activity)  # Add at beginning
        stats_data["recent_activity"] = recent_activity[:10]  # Keep only most recent 10

        # Update player count if needed
        stats_data["player_count"] = max(stats_data["player_count"], player_count)

        # Save updated statistics
        try:
            # Ensure the data directory exists
            os.makedirs(os.path.dirname(stats_path), exist_ok=True)

            # Save the stats data to file
            with open(stats_path, 'w') as f:
                json.dump(stats_data, f, indent=4)
            print(f"Game start event added to {stats_path}")
        except Exception as e:
            print(f"Error saving updated statistics: {str(e)}")

    def _generate_sample_game_history(self):
        """
        Generate sample game history data for testing when database is empty.

        Returns:
            List of sample game history records
        """
        from datetime import datetime, timedelta
        import random

        sample_history = []

        # Generate 15 sample games over the last 3 days
        base_date = datetime.now()

        for i in range(15):
            # Distribute games over the last 3 days
            days_ago = random.randint(0, 2)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)

            game_date = base_date - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)

            # Sample player names
            player_names = ["Alice", "Bob", "Charlie", "Diana", "Eve", "Frank", "Grace", "Henry", "Ivy", "Jack"]
            winner_name = random.choice(player_names)

            # Sample claim types
            claim_types = ["Full House", "First Line", "Second Line", "Four Corners", "Center Cross"]
            claim_type = random.choice(claim_types)

            # Generate sample game record
            game_record = {
                'id': i + 1,
                'session_id': f"Session-{(i // 5) + 1}",  # Group every 5 games into a session
                'game_date': game_date.strftime('%Y-%m-%d %H:%M'),
                'winner_name': winner_name,
                'winner_cartella': random.randint(1, 6),
                'claim_type': claim_type,
                'game_duration': random.randint(300, 1800),  # 5-30 minutes
                'player_count': random.randint(3, 12),
                'prize_amount': random.randint(100, 500),
                'commission_percentage': random.choice([5, 10, 15]),
                'total_bet_amount': random.randint(150, 600),
                'called_numbers_count': random.randint(15, 45)
            }

            sample_history.append(game_record)

        # Sort by date (most recent first)
        sample_history.sort(key=lambda x: x['game_date'], reverse=True)

        print(f"DEBUG: Generated {len(sample_history)} sample game history records")
        return sample_history

    def is_authenticated(self):
        """
        Check if user is authenticated and session is still valid.

        Returns:
            bool: True if authenticated and session is valid
        """
        if not self.authenticated:
            return False

        # Check session timeout
        current_time = time.time()
        if current_time - self.auth_session_start > self.auth_session_timeout:
            self.logout()
            return False

        return True

    def authenticate_user(self, username, password):
        """
        Authenticate user with username and password.

        Args:
            username: Username to authenticate
            password: Password to authenticate

        Returns:
            bool: True if authentication successful
        """
        # Check default credentials
        if username == "wowbingo" and password == "wowgames":
            self.authenticated = True
            self.auth_username = username
            self.auth_session_start = time.time()
            self.login_error_message = ""
            self.login_error_timer = 0
            print(f"User '{username}' authenticated successfully with default credentials")
            return True

        # Try to authenticate with existing admin system if available
        try:
            if STATS_DB_AVAILABLE:
                from stats_db import get_stats_db_manager
                # Use hybrid DB if available
                if HYBRID_DB_AVAILABLE:
                    stats_db = get_hybrid_db_integration()
                else:
                    stats_db = get_stats_db_manager()

                # Try to authenticate as admin user
                user_data = stats_db.authenticate_admin_user(username, password)
                if user_data:
                    self.authenticated = True
                    self.auth_username = username
                    self.auth_session_start = time.time()
                    self.login_error_message = ""
                    self.login_error_timer = 0
                    print(f"User '{username}' authenticated successfully with admin credentials")
                    return True
        except Exception as e:
            print(f"Error authenticating with admin system: {e}")

        # Authentication failed
        self.login_error_message = "Invalid username or password"
        self.login_error_timer = 180  # Show error for 3 seconds
        print(f"Authentication failed for user '{username}'")
        return False

    def logout(self):
        """Logout current user and clear session."""
        print(f"User '{self.auth_username}' logged out")
        self.authenticated = False
        self.auth_username = ""
        self.auth_session_start = 0
        self.login_username = ""
        self.login_password = ""
        self.login_username_active = False
        self.login_password_active = False
        self.login_error_message = ""
        self.login_error_timer = 0

    def draw_login_screen(self, screen_width, screen_height):
        """
        Draw the login screen for stats page authentication.

        Args:
            screen_width: Width of the screen
            screen_height: Height of the screen
        """
        # Draw WOW BINGO header
        header_height = self.draw_wow_bingo_header()

        # Calculate login form dimensions
        form_width = int(400 * self.scale_x)
        form_height = int(350 * self.scale_y)
        form_x = (screen_width - form_width) // 2
        form_y = header_height + int(50 * self.scale_y)

        # Draw form background
        form_rect = pygame.Rect(form_x, form_y, form_width, form_height)
        pygame.draw.rect(self.screen, CARD_BG, form_rect, border_radius=10)
        pygame.draw.rect(self.screen, (80, 120, 160), form_rect, 3, border_radius=10)

        # Draw title
        title_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)
        title_text = title_font.render("Statistics Access", True, WHITE)
        title_rect = title_text.get_rect(centerx=form_x + form_width//2, y=form_y + int(20 * self.scale_y))
        self.screen.blit(title_text, title_rect)

        # Draw subtitle
        subtitle_font = self.get_font("Arial", self.scaled_font_size(14))
        subtitle_text = subtitle_font.render("Please login to access statistics", True, (200, 200, 200))
        subtitle_rect = subtitle_text.get_rect(centerx=form_x + form_width//2, y=title_rect.bottom + int(10 * self.scale_y))
        self.screen.blit(subtitle_text, subtitle_rect)

        # Input field dimensions
        input_width = int(300 * self.scale_x)
        input_height = int(40 * self.scale_y)
        input_x = form_x + (form_width - input_width) // 2

        # Username field
        username_y = subtitle_rect.bottom + int(30 * self.scale_y)
        username_rect = pygame.Rect(input_x, username_y, input_width, input_height)

        # Store for click detection
        self.login_username_rect = username_rect

        # Draw username field
        username_bg_color = (255, 255, 255) if self.login_username_active else (240, 240, 240)
        username_border_color = (100, 150, 255) if self.login_username_active else (180, 180, 180)
        border_width = 2 if self.login_username_active else 1

        pygame.draw.rect(self.screen, username_bg_color, username_rect, border_radius=5)
        pygame.draw.rect(self.screen, username_border_color, username_rect, border_width, border_radius=5)

        # Username label
        label_font = self.get_font("Arial", self.scaled_font_size(12), bold=True)
        username_label = label_font.render("Username:", True, WHITE)
        username_label_rect = username_label.get_rect(left=input_x, bottom=username_y - int(5 * self.scale_y))
        self.screen.blit(username_label, username_label_rect)

        # Username text
        text_font = self.get_font("Arial", self.scaled_font_size(14))
        if self.login_username:
            username_text = text_font.render(self.login_username, True, (0, 0, 0))
            text_rect = username_text.get_rect(left=input_x + int(10 * self.scale_x), centery=username_y + input_height//2)
            self.screen.blit(username_text, text_rect)

            # Draw cursor if active
            if self.login_username_active:
                cursor_x = text_rect.right + 2
                cursor_y1 = text_rect.top + 2
                cursor_y2 = text_rect.bottom - 2
                pygame.draw.line(self.screen, (0, 0, 0), (cursor_x, cursor_y1), (cursor_x, cursor_y2), 1)
        else:
            # Placeholder text
            placeholder_text = text_font.render("Enter username", True, (150, 150, 150))
            placeholder_rect = placeholder_text.get_rect(left=input_x + int(10 * self.scale_x), centery=username_y + input_height//2)
            self.screen.blit(placeholder_text, placeholder_rect)

        # Password field
        password_y = username_y + input_height + int(20 * self.scale_y)
        password_rect = pygame.Rect(input_x, password_y, input_width, input_height)

        # Store for click detection
        self.login_password_rect = password_rect

        # Draw password field
        password_bg_color = (255, 255, 255) if self.login_password_active else (240, 240, 240)
        password_border_color = (100, 150, 255) if self.login_password_active else (180, 180, 180)
        border_width = 2 if self.login_password_active else 1

        pygame.draw.rect(self.screen, password_bg_color, password_rect, border_radius=5)
        pygame.draw.rect(self.screen, password_border_color, password_rect, border_width, border_radius=5)

        # Password label
        password_label = label_font.render("Password:", True, WHITE)
        password_label_rect = password_label.get_rect(left=input_x, bottom=password_y - int(5 * self.scale_y))
        self.screen.blit(password_label, password_label_rect)

        # Password text (masked)
        if self.login_password:
            masked_password = "*" * len(self.login_password)
            password_text = text_font.render(masked_password, True, (0, 0, 0))
            text_rect = password_text.get_rect(left=input_x + int(10 * self.scale_x), centery=password_y + input_height//2)
            self.screen.blit(password_text, text_rect)

            # Draw cursor if active
            if self.login_password_active:
                cursor_x = text_rect.right + 2
                cursor_y1 = text_rect.top + 2
                cursor_y2 = text_rect.bottom - 2
                pygame.draw.line(self.screen, (0, 0, 0), (cursor_x, cursor_y1), (cursor_x, cursor_y2), 1)
        else:
            # Placeholder text
            placeholder_text = text_font.render("Enter password", True, (150, 150, 150))
            placeholder_rect = placeholder_text.get_rect(left=input_x + int(10 * self.scale_x), centery=password_y + input_height//2)
            self.screen.blit(placeholder_text, placeholder_rect)

        # Login button
        button_width = int(150 * self.scale_x)
        button_height = int(40 * self.scale_y)
        button_x = form_x + (form_width - button_width) // 2
        button_y = password_y + input_height + int(30 * self.scale_y)

        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        self.login_button_rect = button_rect

        # Button color based on hover state
        button_color = (100, 150, 200) if hasattr(self, 'login_button_hover') and self.login_button_hover else (80, 120, 160)
        pygame.draw.rect(self.screen, button_color, button_rect, border_radius=5)

        # Button text
        button_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        button_text = button_font.render("Login", True, WHITE)
        button_text_rect = button_text.get_rect(center=button_rect.center)
        self.screen.blit(button_text, button_text_rect)

        # Back button
        back_button_width = int(100 * self.scale_x)
        back_button_height = int(35 * self.scale_y)
        back_button_x = form_x + int(20 * self.scale_x)
        back_button_y = button_y + button_height + int(20 * self.scale_y)

        back_button_rect = pygame.Rect(back_button_x, back_button_y, back_button_width, back_button_height)
        self.login_back_button_rect = back_button_rect

        # Back button color based on hover state
        back_button_color = (120, 120, 120) if hasattr(self, 'login_back_button_hover') and self.login_back_button_hover else (100, 100, 100)
        pygame.draw.rect(self.screen, back_button_color, back_button_rect, border_radius=5)
        pygame.draw.rect(self.screen, (150, 150, 150), back_button_rect, 2, border_radius=5)

        # Back button text with arrow
        back_button_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        back_text = f"← Back"
        back_button_text = back_button_font.render(back_text, True, WHITE)
        back_button_text_rect = back_button_text.get_rect(center=back_button_rect.center)
        self.screen.blit(back_button_text, back_button_text_rect)

        # Error message
        if self.login_error_message and self.login_error_timer > 0:
            error_y = back_button_y + back_button_height + int(15 * self.scale_y)
            error_font = self.get_font("Arial", self.scaled_font_size(12), bold=True)
            error_text = error_font.render(self.login_error_message, True, (255, 100, 100))
            error_rect = error_text.get_rect(centerx=form_x + form_width//2, y=error_y)
            self.screen.blit(error_text, error_rect)

            # Decrease error timer
            self.login_error_timer -= 1

    def handle_login_input(self, event):
        """
        Handle keyboard input for login screen.

        Args:
            event: Pygame keyboard event

        Returns:
            bool: True if input was handled and redraw is needed
        """
        if event.key == pygame.K_TAB:
            # Switch between username and password fields
            if self.login_username_active:
                self.login_username_active = False
                self.login_password_active = True
            else:
                self.login_username_active = True
                self.login_password_active = False
            return True

        elif event.key == pygame.K_RETURN:
            # Attempt login
            if self.login_username and self.login_password:
                if self.authenticate_user(self.login_username, self.login_password):
                    # Load statistics after successful login
                    self.load_statistics()
                return True

        elif event.key == pygame.K_BACKSPACE:
            # Delete character
            if self.login_username_active and self.login_username:
                self.login_username = self.login_username[:-1]
                return True
            elif self.login_password_active and self.login_password:
                self.login_password = self.login_password[:-1]
                return True

        elif event.unicode.isprintable() and len(event.unicode) == 1:
            # Add character
            if self.login_username_active:
                if len(self.login_username) < 50:  # Limit username length
                    self.login_username += event.unicode
                    return True
            elif self.login_password_active:
                if len(self.login_password) < 50:  # Limit password length
                    self.login_password += event.unicode
                    return True

        return False

    def handle_login_mouse_click(self, pos):
        """
        Handle mouse clicks on login screen.

        Args:
            pos: Mouse click position (x, y)

        Returns:
            bool: True if click was handled and redraw is needed
        """
        # Check username field click
        if hasattr(self, 'login_username_rect') and self.login_username_rect.collidepoint(pos):
            self.login_username_active = True
            self.login_password_active = False
            return True

        # Check password field click
        if hasattr(self, 'login_password_rect') and self.login_password_rect.collidepoint(pos):
            self.login_username_active = False
            self.login_password_active = True
            return True

        # Check login button click
        if hasattr(self, 'login_button_rect') and self.login_button_rect.collidepoint(pos):
            if self.login_username and self.login_password:
                if self.authenticate_user(self.login_username, self.login_password):
                    # Load statistics after successful login
                    self.load_statistics()
                return True

        # Check back button click
        if hasattr(self, 'login_back_button_rect') and self.login_back_button_rect.collidepoint(pos):
            # Navigate back to the previous page
            self.navigate_back_to_previous_page()
            return True

        # Click outside fields - deactivate all
        self.login_username_active = False
        self.login_password_active = False
        return True

    def handle_login_mouse_motion(self, pos):
        """
        Handle mouse motion on login screen for hover effects.

        Args:
            pos: Mouse position (x, y)

        Returns:
            bool: True if hover state changed and redraw is needed
        """
        redraw_needed = False

        # Check login button hover
        if hasattr(self, 'login_button_rect'):
            old_hover = getattr(self, 'login_button_hover', False)
            new_hover = self.login_button_rect.collidepoint(pos)
            if old_hover != new_hover:
                self.login_button_hover = new_hover
                redraw_needed = True

        # Check back button hover
        if hasattr(self, 'login_back_button_rect'):
            old_hover = getattr(self, 'login_back_button_hover', False)
            new_hover = self.login_back_button_rect.collidepoint(pos)
            if old_hover != new_hover:
                self.login_back_button_hover = new_hover
                redraw_needed = True

        # Check logout button hover (if visible)
        if hasattr(self, 'logout_button_rect'):
            old_hover = getattr(self, 'logout_button_hover', False)
            new_hover = self.logout_button_rect.collidepoint(pos)
            if old_hover != new_hover:
                self.logout_button_hover = new_hover
                redraw_needed = True

        # Check export button hover (if visible)
        if hasattr(self, 'export_button_rect'):
            old_hover = getattr(self, 'export_button_hover', False)
            new_hover = self.export_button_rect.collidepoint(pos)
            if old_hover != new_hover:
                self.export_button_hover = new_hover
                redraw_needed = True

        return redraw_needed

    def navigate_back_to_previous_page(self):
        """
        Navigate back to the previous page that opened the stats page.
        """
        try:
            if self.previous_page == "board_selection":
                # Navigate back to board selection page
                if self.on_close_callback:
                    self.running = False
                    self.on_close_callback()
                else:
                    # If no callback, try to import and show board selection
                    try:
                        from Board_selection_fixed import show_board_selection
                        self.running = False
                        show_board_selection(self.screen)
                    except ImportError:
                        print("Could not import board selection module")
                        self.running = False
            elif self.previous_page == "main_game":
                # Navigate back to main game
                if self.on_close_callback:
                    self.running = False
                    self.on_close_callback()
                else:
                    # If no callback, just close the stats page
                    self.running = False
            elif self.previous_page == "settings":
                # Navigate back to settings page
                if self.on_close_callback:
                    self.running = False
                    self.on_close_callback()
                else:
                    # If no callback, try to import and show settings page
                    try:
                        from settings_page import show_settings_page
                        self.running = False
                        show_settings_page(self.screen)
                    except ImportError:
                        print("Could not import settings page module")
                        self.running = False
            else:
                # Unknown previous page or default case
                if self.on_close_callback:
                    self.running = False
                    self.on_close_callback()
                else:
                    # Default to board selection
                    try:
                        from Board_selection_fixed import show_board_selection
                        self.running = False
                        show_board_selection(self.screen)
                    except ImportError:
                        print("Could not import board selection module")
                        self.running = False
        except Exception as e:
            print(f"Error navigating back to previous page: {e}")
            # Fallback: just close the stats page
            self.running = False

    def draw_logout_button(self, screen_width, header_height):
        """
        Draw logout button in the header area.

        Args:
            screen_width: Width of the screen
            header_height: Height of the header area
        """
        # Logout button dimensions
        button_width = int(80 * self.scale_x)
        button_height = int(30 * self.scale_y)
        button_x = screen_width - button_width - int(20 * self.scale_x)
        button_y = int(10 * self.scale_y)

        # Store for click detection
        logout_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        self.logout_button_rect = logout_rect

        # Button color based on hover state
        button_color = (200, 100, 100) if hasattr(self, 'logout_button_hover') and self.logout_button_hover else (160, 80, 80)
        pygame.draw.rect(self.screen, button_color, logout_rect, border_radius=5)

        # Button text
        button_font = self.get_font("Arial", self.scaled_font_size(12), bold=True)
        button_text = button_font.render("Logout", True, WHITE)
        button_text_rect = button_text.get_rect(center=logout_rect.center)
        self.screen.blit(button_text, button_text_rect)

        # User info
        if self.auth_username:
            user_font = self.get_font("Arial", self.scaled_font_size(10))
            user_text = user_font.render(f"Logged in as: {self.auth_username}", True, WHITE)
            user_rect = user_text.get_rect(right=button_x - int(10 * self.scale_x), centery=button_y + button_height//2)
            self.screen.blit(user_text, user_rect)

    def draw_time_period_tabs(self, start_y):
        """
        Draw time period selection tabs (Daily, Weekly, Monthly, Yearly).

        Args:
            start_y: Y position to start drawing tabs

        Returns:
            int: Height of the tabs section
        """
        screen_width = self._layout['screen_width']

        # Tab dimensions
        tab_width = int(120 * self.scale_x)
        tab_height = int(40 * self.scale_y)
        tab_spacing = int(5 * self.scale_x)
        total_tabs_width = len(self.time_period_tabs) * tab_width + (len(self.time_period_tabs) - 1) * tab_spacing

        # Add refresh button
        refresh_button_width = int(40 * self.scale_x)
        refresh_button_height = tab_height
        refresh_button_spacing = int(15 * self.scale_x)
        
        # Adjust total width to include refresh button
        total_width = total_tabs_width + refresh_button_width + refresh_button_spacing

        # Center everything
        tabs_start_x = (screen_width - total_width) // 2

        # Store tab rectangles for click detection
        if not hasattr(self, 'time_period_tab_rects'):
            self.time_period_tab_rects = {}
            
        # Draw refresh button
        refresh_button_x = tabs_start_x + total_tabs_width + refresh_button_spacing
        refresh_button_y = start_y
        refresh_button_rect = pygame.Rect(refresh_button_x, refresh_button_y, refresh_button_width, refresh_button_height)
        
        # Store refresh button rect for click detection
        self.time_period_refresh_rect = refresh_button_rect
        
        # Draw the refresh button
        pygame.draw.rect(self.screen, (60, 100, 140), refresh_button_rect, border_radius=5)
        pygame.draw.rect(self.screen, (80, 120, 160), refresh_button_rect, 2, border_radius=5)
        
        # Draw refresh icon (circular arrow)
        center_x = refresh_button_x + refresh_button_width // 2
        center_y = refresh_button_y + refresh_button_height // 2
        radius = min(refresh_button_width, refresh_button_height) // 3
        
        # Draw arrow circle
        pygame.draw.circle(self.screen, WHITE, (center_x, center_y), radius, 2)
        
        # Draw arrow head (triangle)
        arrow_size = radius // 2
        arrow_points = [
            (center_x, center_y - radius - arrow_size),
            (center_x - arrow_size, center_y - radius + arrow_size),
            (center_x + arrow_size, center_y - radius + arrow_size)
        ]
        pygame.draw.polygon(self.screen, WHITE, arrow_points)

        # Draw each tab with proper colors
        for i, tab_name in enumerate(self.time_period_tabs):
            tab_x = tabs_start_x + i * (tab_width + tab_spacing)
            tab_y = start_y

            tab_rect = pygame.Rect(tab_x, tab_y, tab_width, tab_height)
            self.time_period_tab_rects[tab_name.lower()] = tab_rect
            
            # Tab color based on selection
            is_active = self.current_time_period == tab_name.lower()
            if is_active:
                tab_color = (80, 120, 160)
                text_color = WHITE
                border_width = 3
            else:
                tab_color = CARD_BG
                text_color = (200, 200, 200)
                border_width = 1

            # Draw tab
            pygame.draw.rect(self.screen, tab_color, tab_rect, border_radius=5)
            pygame.draw.rect(self.screen, (100, 140, 180), tab_rect, border_width, border_radius=5)

            # Tab text
            tab_font = self.get_font("Arial", self.scaled_font_size(14), bold=is_active)
            tab_text = tab_font.render(tab_name, True, text_color)
            tab_text_rect = tab_text.get_rect(center=tab_rect.center)
            self.screen.blit(tab_text, tab_text_rect)

        return tab_height + int(20 * self.scale_y)

    def draw_time_period_data(self, start_y):
        """
        Draw data for the selected time period.

        Args:
            start_y: Y position to start drawing data
        """
        if self.current_time_period == "weekly":
            # Use existing weekly earnings method
            self.draw_weekly_earnings(start_y)
        elif self.current_time_period == "daily":
            self.draw_daily_earnings(start_y)
        elif self.current_time_period == "monthly":
            self.draw_monthly_earnings(start_y)
        elif self.current_time_period == "yearly":
            self.draw_yearly_earnings(start_y)

    def draw_daily_earnings(self, start_y):
        """FIXED: Draw daily earnings for the last 7 days with consistent date detection."""
        screen_width = self._layout['screen_width']

        # Calculate card dimensions (compact and properly fitted)
        total_margin = int(40 * self.scale_x)  # Left and right margins
        card_spacing = int(6 * self.scale_x)  # Reduced spacing
        available_width = screen_width - total_margin
        total_spacing = 7 * card_spacing  # 7 spaces between 8 cards
        card_width = int((available_width - total_spacing) / 8)  # 8 cards total
        card_height = int(60 * self.scale_y)  # Reduced height

        # PERFORMANCE: Use cached weekly stats to prevent UI blocking
        if hasattr(self, 'weekly_stats') and self.weekly_stats:
            weekly_stats = self.weekly_stats
            print(f"PERFORMANCE: Drawing daily earnings with cached data ({len(weekly_stats)} days)")
        else:
            # PERFORMANCE: Use fallback data instead of blocking database call
            from datetime import datetime, timedelta
            today = datetime.now()
            weekly_stats = []

            # FIXED: Generate correct date range for current week (last 7 days including today)
            for i in range(7):
                day = today - timedelta(days=6-i)  # This gives us 7 days ending today
                weekly_stats.append({
                    'date': day.strftime('%Y-%m-%d'),
                    'games_played': 0,
                    'earnings': 0.0
                })

            print(f"PERFORMANCE: Drawing daily earnings with fallback data for dates {weekly_stats[0]['date']} to {weekly_stats[-1]['date']}")
            print(f"PERFORMANCE: Today is {today.strftime('%Y-%m-%d')}, showing last 7 days")

        # Generate daily data from centralized weekly stats
        daily_data = []
        for i, day_stats in enumerate(weekly_stats):
            day_date = day_stats.get('date', '')
            day_earnings = day_stats.get('earnings', 0)

            # Convert date to day name
            try:
                from datetime import datetime
                dt = datetime.strptime(day_date, '%Y-%m-%d')
                day_name = dt.strftime('%a')  # Shorter format for better display
            except:
                day_name = f"Day{i+1}"

            daily_data.append({
                'name': day_name,
                'earnings': day_earnings,
                'date': day_date
            })
            print(f"CENTRALIZED: {day_name} ({day_date}): {day_earnings:.2f} ETB")

        # Draw each day card
        for i, day_data in enumerate(daily_data):
            card_x = int(20 * self.scale_x) + i * (card_width + card_spacing)
            card_y = start_y

            color = self.day_colors[i] if i < len(self.day_colors) else (100, 150, 200)
            self.draw_day_card(card_x, card_y, card_width, card_height,
                             day_data['name'], day_data['earnings'], color)

        # CENTRALIZED: Draw total card with unified calculation
        total_x = int(20 * self.scale_x) + 7 * (card_width + card_spacing)
        total_y = start_y
        total_earnings = sum(day['earnings'] for day in daily_data)
        print(f"CENTRALIZED: Total daily earnings: {total_earnings:.2f} ETB")
        self.draw_day_card(total_x, total_y, card_width, card_height,
                         "Total", total_earnings, TOTAL_CARD_BG, is_total=True)

    def draw_monthly_earnings(self, start_y):
        """Draw monthly earnings for the last 6 months using real data."""
        screen_width = self._layout['screen_width']

        # Calculate card dimensions
        card_width = int(screen_width / 7) - int(10 * self.scale_x)
        card_height = int(80 * self.scale_y)
        card_spacing = int(10 * self.scale_x)

        # CENTRALIZED: Get monthly stats from the centralized provider
        if not hasattr(self, 'stats_provider'):
            if EMERGENCY_STATS_AVAILABLE:
                self.stats_provider = get_emergency_stats_provider()
                print("Using EMERGENCY ultra-fast stats provider")
            elif PERFORMANCE_STATS_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif EMERGENCY_STATS_AVAILABLE:
                self.stats_provider = get_emergency_stats_provider()
                print("Using EMERGENCY ultra-fast stats provider")
            elif PERFORMANCE_STATS_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif PERFORMANCE_OPTIMIZED_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif SIMPLE_STATS_AVAILABLE:
                self.stats_provider = get_simple_stats_provider()
                print("Using simple stats provider")
            else:
                self.stats_provider = CentralizedStatsProvider()
                print("Using original stats provider")
        
        # Get real monthly data
        monthly_data = self.stats_provider.get_monthly_stats()
        
        if not monthly_data:
            print("CENTRALIZED: No monthly data available, using fallback")
            # Fallback to empty data
            monthly_data = []
            today = datetime.now()
            
            for i in range(6):
                # Calculate month
                if i > 0:
                    month_date = today.replace(day=1)
                    for _ in range(i):
                        month_date = month_date.replace(day=1) - timedelta(days=1)
                else:
                    month_date = today
                
                month_name = month_date.strftime('%b %Y')
                monthly_data.append({'name': month_name, 'earnings': 0.0})

        # Define consistent month colors
        month_colors = [
            (100, 150, 200),  # January-like blue
            (120, 170, 180),  # February-like teal
            (140, 190, 160),  # March-like green
            (160, 210, 140),  # April-like light green
            (180, 190, 120),  # May-like yellow-green
            (200, 170, 100)   # June-like orange
        ]
        
        # Draw each month card
        for i, month_data in enumerate(monthly_data):
            card_x = int(20 * self.scale_x) + i * (card_width + card_spacing)
            card_y = start_y

            # Use a consistent color scheme
            color = month_colors[i] if i < len(month_colors) else (100 + i * 20, 150 + i * 10, 200 - i * 10)
            
            # Print debug info
            print(f"CENTRALIZED: Month {month_data['name']}: {month_data['earnings']:.2f} ETB")
            
            self.draw_day_card(card_x, card_y, card_width, card_height,
                             month_data['name'], month_data['earnings'], color)

        # Draw total card
        total_x = int(20 * self.scale_x) + 6 * (card_width + card_spacing)
        total_y = start_y
        total_earnings = sum(month['earnings'] for month in monthly_data)
        print(f"CENTRALIZED: Total monthly earnings: {total_earnings:.2f} ETB")
        self.draw_day_card(total_x, total_y, card_width, card_height,
                         "Total", total_earnings, TOTAL_CARD_BG, is_total=True)

    def draw_yearly_earnings(self, start_y):
        """Draw yearly earnings for the last 3 years using real data."""
        screen_width = self._layout['screen_width']

        # Calculate card dimensions
        card_width = int(screen_width / 4) - int(20 * self.scale_x)
        card_height = int(80 * self.scale_y)
        card_spacing = int(20 * self.scale_x)

        # CENTRALIZED: Get yearly stats from the centralized provider
        if not hasattr(self, 'stats_provider'):
            if EMERGENCY_STATS_AVAILABLE:
                self.stats_provider = get_emergency_stats_provider()
                print("Using EMERGENCY ultra-fast stats provider")
            elif PERFORMANCE_STATS_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif EMERGENCY_STATS_AVAILABLE:
                self.stats_provider = get_emergency_stats_provider()
                print("Using EMERGENCY ultra-fast stats provider")
            elif PERFORMANCE_STATS_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif PERFORMANCE_OPTIMIZED_AVAILABLE:
                self.stats_provider = get_performance_optimized_stats_provider()
                print("Using performance-optimized stats provider")
            elif SIMPLE_STATS_AVAILABLE:
                self.stats_provider = get_simple_stats_provider()
                print("Using simple stats provider")
            else:
                self.stats_provider = CentralizedStatsProvider()
                print("Using original stats provider")
        
        # Get real yearly data
        yearly_data = self.stats_provider.get_yearly_stats()
        
        if not yearly_data:
            print("CENTRALIZED: No yearly data available, using fallback")
            # Fallback to empty data
            yearly_data = []
            current_year = datetime.now().year
            
            for i in range(3):
                year = current_year - (2 - i)
                year_name = str(year)
                yearly_data.append({'name': year_name, 'earnings': 0.0})

        # Define consistent year colors
        year_colors = [
            (80, 120, 180),   # Past year - blue
            (100, 160, 140),  # Last year - teal
            (120, 180, 100)   # Current year - green
        ]
        
        # Draw each year card
        for i, year_data in enumerate(yearly_data):
            card_x = int(20 * self.scale_x) + i * (card_width + card_spacing)
            card_y = start_y

            # Use a consistent color scheme
            color = year_colors[i] if i < len(year_colors) else (120 + i * 30, 160 + i * 20, 220 - i * 20)
            
            # Print debug info
            print(f"CENTRALIZED: Year {year_data['name']}: {year_data['earnings']:.2f} ETB")
            
            self.draw_day_card(card_x, card_y, card_width, card_height,
                             year_data['name'], year_data['earnings'], color)

        # Draw total card
        total_x = int(20 * self.scale_x) + 3 * (card_width + card_spacing)
        total_y = start_y
        total_earnings = sum(year['earnings'] for year in yearly_data)
        print(f"CENTRALIZED: Total yearly earnings: {total_earnings:.2f} ETB")
        self.draw_day_card(total_x, total_y, card_width, card_height,
                         "Total", total_earnings, TOTAL_CARD_BG, is_total=True)

    def draw_scroll_indicator(self, screen_width, screen_height):
        """
        Draw a scroll indicator on the right side of the screen.

        Args:
            screen_width: Width of the screen
            screen_height: Height of the screen
        """
        if self.max_scroll_y <= 0:
            return

        # Scroll bar dimensions
        scrollbar_width = int(8 * self.scale_x)
        scrollbar_height = screen_height - int(100 * self.scale_y)  # Leave space for header
        scrollbar_x = screen_width - scrollbar_width - int(5 * self.scale_x)
        scrollbar_y = int(50 * self.scale_y)  # Start below header

        # Background track
        track_rect = pygame.Rect(scrollbar_x, scrollbar_y, scrollbar_width, scrollbar_height)
        pygame.draw.rect(self.screen, (40, 40, 40), track_rect, border_radius=4)

        # Calculate thumb position and size
        content_ratio = scrollbar_height / self.content_height
        thumb_height = max(int(scrollbar_height * content_ratio), 20)  # Minimum thumb height

        scroll_ratio = self.scroll_y / self.max_scroll_y if self.max_scroll_y > 0 else 0
        thumb_y = scrollbar_y + int((scrollbar_height - thumb_height) * scroll_ratio)

        # Draw thumb
        thumb_rect = pygame.Rect(scrollbar_x, thumb_y, scrollbar_width, thumb_height)
        pygame.draw.rect(self.screen, (120, 120, 120), thumb_rect, border_radius=4)

        # Draw scroll hint text if near top
        if self.scroll_y < 50:
            hint_font = self.get_font("Arial", self.scaled_font_size(10))
            hint_text = hint_font.render("Scroll down for more", True, (150, 150, 150))
            hint_rect = hint_text.get_rect(right=scrollbar_x - int(10 * self.scale_x),
                                         y=scrollbar_y + scrollbar_height + int(10 * self.scale_y))
            self.screen.blit(hint_text, hint_rect)

    def show_message(self, message, message_type="info"):
        """Show a toast message"""
        self.message = message
        self.message_type = message_type
        self.message_timer = 180  # Display for 3 seconds (60 fps * 3)

    def draw_stats_section(self, x, y, width, height, title, color):
        """Draw an enhanced statistics section with a title and background - optimized version"""
        # Create a unique key for this section
        cache_key = (x, y, width, height, title, color)

        # Check if we have this section cached
        if cache_key in self._section_cache:
            # Get cached surfaces and rects
            section_bg, title_bar, title_text, shadow, content_rect = self._section_cache[cache_key]

            # Draw the cached elements
            self.screen.blit(section_bg, (x, y))
            self.screen.blit(title_bar, (x, y))
            self.screen.blit(shadow, shadow.get_rect(center=(int(x + width/2) + 2, y + int(40 * min(self.scale_x, self.scale_y))/2 + 2)))
            self.screen.blit(title_text, title_text.get_rect(center=(int(x + width/2), y + int(40 * min(self.scale_x, self.scale_y))/2)))

            return content_rect

        # Define section dimensions (no need to create a rect object here)

        # Define gradient colors based on title color
        if color == (255, 180, 0):  # GOLD (Financial)
            color1 = (40, 35, 20)
            color2 = (30, 25, 15)
            border_color = (80, 70, 30)
        elif color == (150, 80, 200):  # PURPLE (Session)
            color1 = (40, 30, 50)
            color2 = (30, 20, 40)
            border_color = (70, 50, 90)
        elif color == (0, 120, 215):  # BLUE (Game)
            color1 = (30, 40, 60)
            color2 = (20, 30, 50)
            border_color = (50, 70, 100)
        elif color == (0, 180, 100):  # GREEN (Player)
            color1 = (30, 50, 40)
            color2 = (20, 40, 30)
            border_color = (50, 90, 60)
        else:
            color1 = (40, 40, 50)
            color2 = (30, 30, 40)
            border_color = (60, 60, 80)

        # Create a surface for the section background
        section_bg = pygame.Surface((width, height), pygame.SRCALPHA)

        # Draw a gradient background with enhanced visual effects
        # Create a temporary rect for the gradient
        temp_rect = pygame.Rect(0, 0, width, height)

        # Draw gradient directly to the surface
        step_size = max(1, height // 50)  # Use at most 50 steps
        for y_pos in range(0, height, step_size):
            # Calculate blend factor (0.0 to 1.0)
            t = y_pos / max(1, height - 1)

            # Height of this segment
            segment_height = min(step_size, height - y_pos)

            # Linear interpolation of colors
            r = int(color1[0] * (1 - t) + color2[0] * t)
            g = int(color1[1] * (1 - t) + color2[1] * t)
            b = int(color1[2] * (1 - t) + color2[2] * t)

            # Draw a rectangle of the gradient
            pygame.draw.rect(section_bg, (r, g, b), (0, y_pos, width, segment_height))

        # Apply rounded corners
        mask = pygame.Surface((width, height), pygame.SRCALPHA)
        pygame.draw.rect(mask, (255, 255, 255), temp_rect, border_radius=10)
        section_bg.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

        # Add subtle glow effect to the border - simplified to just one glow
        glow_surf = pygame.Surface((width + 4, height + 4), pygame.SRCALPHA)
        pygame.draw.rect(glow_surf, (*border_color, 80), pygame.Rect(0, 0, width + 4, height + 4), 1, border_radius=10)
        section_bg.blit(glow_surf, (-2, -2))

        # Draw title bar with the specified color and enhanced gradient
        title_height = int(40 * min(self.scale_x, self.scale_y))

        # Create a surface for the title bar
        title_bar = pygame.Surface((width, title_height), pygame.SRCALPHA)

        # Create a brighter version of the color for the gradient
        bright_color = tuple(min(255, c * 1.2) for c in color)
        dark_color = tuple(c // 2 for c in color)

        # Draw horizontal gradient for title bar (left to right)
        # Create a temporary rect for the gradient
        temp_rect = pygame.Rect(0, 0, width, title_height)

        # Draw gradient directly to the surface - with larger steps for better performance
        step_size = max(1, width // 100)  # Use at most 100 steps
        for x_pos in range(0, width, step_size):
            # Calculate position ratio (0.0 to 1.0)
            t = x_pos / max(1, width - 1)

            # Width of this segment
            segment_width = min(step_size, width - x_pos)

            # Determine which segment we're in
            if t < 0.5:
                # Left half: blend from left to middle
                segment_t = t * 2  # Scale to 0-1 range
                r = int(dark_color[0] * (1 - segment_t) + bright_color[0] * segment_t)
                g = int(dark_color[1] * (1 - segment_t) + bright_color[1] * segment_t)
                b = int(dark_color[2] * (1 - segment_t) + bright_color[2] * segment_t)
            else:
                # Right half: blend from middle to right
                segment_t = (t - 0.5) * 2  # Scale to 0-1 range
                r = int(bright_color[0] * (1 - segment_t) + dark_color[0] * segment_t)
                g = int(bright_color[1] * (1 - segment_t) + dark_color[1] * segment_t)
                b = int(bright_color[2] * (1 - segment_t) + dark_color[2] * segment_t)

            # Draw a vertical rectangle of the gradient
            pygame.draw.rect(title_bar, (r, g, b), (x_pos, 0, segment_width, title_height))

        # Apply rounded corners to the top of the title bar
        mask = pygame.Surface((width, title_height), pygame.SRCALPHA)
        pygame.draw.rect(mask, (255, 255, 255), temp_rect, border_radius=10)
        title_bar.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

        # Draw title text with shadow for better visibility
        title_font = self.get_font("Arial", self.scaled_font_size(20), bold=True)

        # Create shadow text
        shadow = title_font.render(title, True, (0, 0, 0, 150))

        # Create main text
        title_text = title_font.render(title, True, WHITE)

        # Calculate content rectangle
        content_rect = pygame.Rect(x, y + title_height, width, height - title_height)

        # Cache the section elements
        self._section_cache[cache_key] = (section_bg, title_bar, title_text, shadow, content_rect)

        # Limit cache size
        if len(self._section_cache) > 10:  # Keep only 10 most recent sections
            self._section_cache.pop(next(iter(self._section_cache)))

        # Draw the elements to the screen
        self.screen.blit(section_bg, (x, y))
        self.screen.blit(title_bar, (x, y))

        # Draw shadow
        shadow_offset = 2
        shadow_rect = shadow.get_rect(center=(int(x + width/2) + shadow_offset, int(y + title_height/2) + shadow_offset))
        self.screen.blit(shadow, shadow_rect)

        # Draw main text
        title_text_rect = title_text.get_rect(center=(int(x + width/2), int(y + title_height/2)))
        self.screen.blit(title_text, title_text_rect)

        # Return the content area rectangle (below the title)
        return content_rect

    def draw_horizontal_gradient_rect(self, rect, color_left, color_middle, color_right, border_radius=0):
        """Draw a rectangle with a horizontal gradient with three colors - optimized version"""
        # Create a surface with alpha channel
        surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Cache for gradient surfaces
        cache_key = (rect.width, rect.height, color_left, color_middle, color_right, border_radius)
        if hasattr(self, '_gradient_cache') and cache_key in self._gradient_cache:
            # Use cached surface if available
            self.screen.blit(self._gradient_cache[cache_key], rect)
            return

        # Draw gradient by blending colors horizontally - with larger steps for better performance
        step_size = max(1, rect.width // 100)  # Use at most 100 steps

        for x in range(0, rect.width, step_size):
            # Calculate position ratio (0.0 to 1.0)
            t = x / max(1, rect.width - 1)

            # Width of this segment
            segment_width = min(step_size, rect.width - x)

            # Determine which segment we're in
            if t < 0.5:
                # Left half: blend from left to middle
                segment_t = t * 2  # Scale to 0-1 range
                r = int(color_left[0] * (1 - segment_t) + color_middle[0] * segment_t)
                g = int(color_left[1] * (1 - segment_t) + color_middle[1] * segment_t)
                b = int(color_left[2] * (1 - segment_t) + color_middle[2] * segment_t)
            else:
                # Right half: blend from middle to right
                segment_t = (t - 0.5) * 2  # Scale to 0-1 range
                r = int(color_middle[0] * (1 - segment_t) + color_right[0] * segment_t)
                g = int(color_middle[1] * (1 - segment_t) + color_right[1] * segment_t)
                b = int(color_middle[2] * (1 - segment_t) + color_right[2] * segment_t)

            # Draw a vertical rectangle of the gradient (more efficient than lines)
            pygame.draw.rect(surf, (r, g, b), (x, 0, segment_width, rect.height))

        # Apply rounded corners if requested
        if border_radius > 0:
            # Create mask for rounded corners
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius)

            # Apply mask
            surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

        # Cache the surface for future use
        if not hasattr(self, '_gradient_cache'):
            self._gradient_cache = {}

        # Limit cache size to prevent memory issues
        if len(self._gradient_cache) > 20:  # Keep only 20 most recent gradients
            self._gradient_cache.pop(next(iter(self._gradient_cache)))

        self._gradient_cache[cache_key] = surf.copy()

        # Draw the surface to the screen
        self.screen.blit(surf, rect)

    def draw_gradient_rect(self, rect, color1, color2, border_radius=0):
        """Draw a rectangle with a vertical gradient - optimized version"""
        # Create a surface with alpha channel
        surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Cache for gradient surfaces
        cache_key = (rect.width, rect.height, color1, color2, border_radius)
        if hasattr(self, '_gradient_cache') and cache_key in self._gradient_cache:
            # Use cached surface if available
            self.screen.blit(self._gradient_cache[cache_key], rect)
            return

        # Draw gradient by blending two colors over height - with larger steps for better performance
        step_size = max(1, rect.height // 50)  # Use at most 50 steps

        for y in range(0, rect.height, step_size):
            # Calculate blend factor (0.0 to 1.0)
            t = y / max(1, rect.height - 1)

            # Height of this segment
            segment_height = min(step_size, rect.height - y)

            # Linear interpolation of colors
            r = int(color1[0] * (1 - t) + color2[0] * t)
            g = int(color1[1] * (1 - t) + color2[1] * t)
            b = int(color1[2] * (1 - t) + color2[2] * t)

            # Draw a rectangle of the gradient (more efficient than lines)
            pygame.draw.rect(surf, (r, g, b), (0, y, rect.width, segment_height))

        # Apply rounded corners if requested
        if border_radius > 0:
            # Create mask for rounded corners
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius)

            # Apply mask
            surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

        # Cache the surface for future use
        if not hasattr(self, '_gradient_cache'):
            self._gradient_cache = {}

        # Limit cache size to prevent memory issues
        if len(self._gradient_cache) > 20:  # Keep only 20 most recent gradients
            self._gradient_cache.pop(next(iter(self._gradient_cache)))

        self._gradient_cache[cache_key] = surf.copy()

        # Draw the surface to the screen
        self.screen.blit(surf, rect)

    def draw_game_stats_section(self, x, y, width, height):
        """Draw the Game Statistics section"""
        # Draw the section with a blue title
        content_rect = self.draw_stats_section(x, y, width, height, "GAME STATISTICS", (0, 120, 215))

        # Calculate layout for stats items
        padding = int(15 * min(self.scale_x, self.scale_y))
        item_height = int(30 * min(self.scale_x, self.scale_y))
        start_y = content_rect.y + padding

        # Prepare fonts
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
        value_font = pygame.font.SysFont("Arial", self.scaled_font_size(20), bold=True)

        # Draw games played
        self.draw_stat_item(content_rect.x + padding, start_y,
                          "Games Played:", str(self.stats_data["games_played"]),
                          label_font, value_font, LIGHT_BLUE)

        # Draw total winners
        self.draw_stat_item(content_rect.x + padding, start_y + item_height,
                          "Total Winners:", str(self.stats_data["total_winners"]),
                          label_font, value_font, LIGHT_BLUE)

        # Draw average game duration
        avg_duration = self.stats_data["average_game_duration"]
        if avg_duration > 0:
            minutes = int(avg_duration // 60)
            seconds = int(avg_duration % 60)
            duration_str = f"{minutes}m {seconds}s"
        else:
            duration_str = "N/A"

        self.draw_stat_item(content_rect.x + padding, start_y + item_height * 2,
                          "Average Duration:", duration_str,
                          label_font, value_font, LIGHT_BLUE)

        # Draw most frequent numbers
        freq_numbers = self.stats_data["number_frequencies"]
        if freq_numbers:
            # Find the most frequent numbers
            sorted_numbers = sorted(freq_numbers.items(), key=lambda x: x[1], reverse=True)
            top_numbers = sorted_numbers[:5]  # Get top 5 numbers

            # Draw the title for most frequent numbers
            freq_title = label_font.render("Most Frequent Numbers:", True, LIGHT_BLUE)
            self.screen.blit(freq_title, (content_rect.x + padding, start_y + item_height * 3))

            # Draw the top numbers in a row
            number_y = start_y + item_height * 4
            circle_size = int(25 * min(self.scale_x, self.scale_y))
            number_spacing = int(circle_size * 1.5)

            for i, (number, freq) in enumerate(top_numbers):
                # Calculate position
                circle_x = content_rect.x + padding + i * number_spacing + circle_size // 2

                # Draw circle
                pygame.draw.circle(self.screen, BLUE, (circle_x, number_y), circle_size // 2)

                # Draw number
                num_font = pygame.font.SysFont("Arial", self.scaled_font_size(14), bold=True)
                num_text = num_font.render(str(number), True, WHITE)
                num_rect = num_text.get_rect(center=(circle_x, number_y))
                self.screen.blit(num_text, num_rect)

                # Draw frequency below
                freq_text = label_font.render(f"{freq}x", True, LIGHT_GRAY)
                freq_rect = freq_text.get_rect(center=(circle_x, number_y + circle_size // 2 + int(12 * min(self.scale_x, self.scale_y))))
                self.screen.blit(freq_text, freq_rect)
        else:
            # No frequency data available
            no_data = label_font.render("No number frequency data available", True, LIGHT_GRAY)
            no_data_rect = no_data.get_rect(x=content_rect.x + padding, y=start_y + item_height * 3)
            self.screen.blit(no_data, no_data_rect)

    def draw_player_stats_section(self, x, y, width, height):
        """Draw the Player Statistics section"""
        # Draw the section with a green title
        content_rect = self.draw_stats_section(x, y, width, height, "PLAYER STATISTICS", (0, 180, 100))

        # Calculate layout for stats items
        padding = int(15 * min(self.scale_x, self.scale_y))
        item_height = int(30 * min(self.scale_x, self.scale_y))
        start_y = content_rect.y + padding

        # Prepare fonts
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
        value_font = pygame.font.SysFont("Arial", self.scaled_font_size(20), bold=True)

        # Draw total players
        self.draw_stat_item(content_rect.x + padding, start_y,
                          "Total Players:", str(self.stats_data["player_count"]),
                          label_font, value_font, (100, 220, 100))

        # Draw active players (if available)
        active_players = len(self.players) if hasattr(self, 'players') and self.players else 0
        self.draw_stat_item(content_rect.x + padding, start_y + item_height,
                          "Active Players:", str(active_players),
                          label_font, value_font, (100, 220, 100))

        # Draw top players section
        top_players_title = label_font.render("Top Players:", True, (100, 220, 100))
        self.screen.blit(top_players_title, (content_rect.x + padding, start_y + item_height * 2))

        # Check if we have player data
        if hasattr(self, 'players') and self.players:
            # Sort players by wins (or another metric)
            sorted_players = sorted(self.players, key=lambda p: getattr(p, 'wins', 0) if hasattr(p, 'wins') else 0, reverse=True)
            top_players = sorted_players[:5]  # Get top 5 players

            # Draw player list
            player_y = start_y + item_height * 3
            for i, player in enumerate(top_players):
                # Get player name and wins
                player_name = getattr(player, 'name', f"Player {i+1}") if hasattr(player, 'name') else f"Player {i+1}"
                player_wins = getattr(player, 'wins', 0) if hasattr(player, 'wins') else 0

                # Draw player entry
                player_text = label_font.render(f"{i+1}. {player_name}", True, WHITE)
                self.screen.blit(player_text, (content_rect.x + padding, player_y + i * item_height))

                # Draw wins
                wins_text = label_font.render(f"{player_wins} wins", True, LIGHT_GRAY)
                wins_x = content_rect.x + width - padding - wins_text.get_width()
                self.screen.blit(wins_text, (wins_x, player_y + i * item_height))
        else:
            # No player data available
            no_data = label_font.render("No player data available", True, LIGHT_GRAY)
            no_data_rect = no_data.get_rect(x=content_rect.x + padding, y=start_y + item_height * 3)
            self.screen.blit(no_data, no_data_rect)

    def draw_financial_stats_section(self, x, y, width, height):
        """Draw the Financial Statistics section"""
        # Draw the section with a gold title
        content_rect = self.draw_stats_section(x, y, width, height, "FINANCIAL STATISTICS", (255, 180, 0))

        # Calculate layout for stats items
        padding = int(15 * min(self.scale_x, self.scale_y))
        item_height = int(30 * min(self.scale_x, self.scale_y))
        start_y = content_rect.y + padding

        # Prepare fonts
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
        value_font = pygame.font.SysFont("Arial", self.scaled_font_size(20), bold=True)
        small_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))

        # Draw total prize pool
        prize_pool = self.stats_data["total_prize_pool"]
        prize_pool_str = f"{prize_pool:,} ETB" if prize_pool > 0 else "0 ETB"
        self.draw_stat_item(content_rect.x + padding, start_y,
                          "Total Prize Pool:", prize_pool_str,
                          label_font, value_font, GOLD)

        # Draw average bet amount (if available)
        avg_bet = 0
        if hasattr(self, 'players') and self.players:
            total_bets = sum(getattr(p, 'bet_amount', 0) for p in self.players if hasattr(p, 'bet_amount'))
            player_count = len([p for p in self.players if hasattr(p, 'bet_amount') and getattr(p, 'bet_amount', 0) > 0])
            avg_bet = total_bets / player_count if player_count > 0 else 0

        avg_bet_str = f"{avg_bet:,.2f} ETB" if avg_bet > 0 else "N/A"
        self.draw_stat_item(content_rect.x + padding, start_y + item_height,
                          "Average Bet:", avg_bet_str,
                          label_font, value_font, GOLD)

        # Draw total revenue (if available)
        revenue = 0
        if hasattr(self, 'players') and self.players:
            revenue = sum(getattr(p, 'bet_amount', 0) for p in self.players if hasattr(p, 'bet_amount'))

        revenue_str = f"{revenue:,} ETB" if revenue > 0 else "0 ETB"
        self.draw_stat_item(content_rect.x + padding, start_y + item_height * 2,
                          "Total Revenue:", revenue_str,
                          label_font, value_font, GOLD)

        # Check if payment system is available
        if hasattr(self, 'recharge_ui') and hasattr(self, 'recharge_button'):
            # Get voucher manager
            voucher_manager = self.recharge_ui.voucher_manager

            # Get the most recent voucher's share percentage
            voucher_history = voucher_manager.get_voucher_history(1)
            share_percentage = voucher_history[0]['share'] if voucher_history else 30  # Default to 30% if no history

            # Draw commission info
            commission_title = label_font.render("Credit Usage Formula:", True, GOLD)
            self.screen.blit(commission_title, (content_rect.x + padding, start_y + item_height * 3))

            # Draw formula explanation
            formula_y = start_y + item_height * 4
            formula_text = small_font.render("Credits Used = Referee Commission × Share Percentage", True, WHITE)
            self.screen.blit(formula_text, (content_rect.x + padding + 10, formula_y))

            # Draw example calculation
            example_y = formula_y + item_height * 0.8
            example_text1 = small_font.render("Example: 1000 ETB total bets with 20% commission and", True, LIGHT_GRAY)
            self.screen.blit(example_text1, (content_rect.x + padding + 10, example_y))

            example_text2 = small_font.render(f"{share_percentage}% share = {round(1000 * 0.2 * share_percentage / 100)} credits", True, LIGHT_GRAY)
            self.screen.blit(example_text2, (content_rect.x + padding + 10, example_y + item_height * 0.6))

            # Draw current commission settings
            settings_y = example_y + item_height * 1.4
            commission_text = small_font.render(f"Current Commission: 20% | Share: {share_percentage}%", True, GOLD)
            self.screen.blit(commission_text, (content_rect.x + padding + 10, settings_y))

            # Draw chart title
            chart_title = label_font.render("Financial Overview:", True, GOLD)
            self.screen.blit(chart_title, (content_rect.x + padding, settings_y + item_height))

            # Draw chart
            chart_y = settings_y + item_height * 2
            chart_height = int(60 * min(self.scale_x, self.scale_y))  # Reduced height to fit new content
            chart_width = width - padding * 2
        else:
            # Draw a simple bar chart for financial data
            chart_title = label_font.render("Financial Overview:", True, GOLD)
            self.screen.blit(chart_title, (content_rect.x + padding, start_y + item_height * 3))

            # Draw chart
            chart_y = start_y + item_height * 4
            chart_height = int(80 * min(self.scale_x, self.scale_y))
            chart_width = width - padding * 2

        # Draw chart background
        chart_bg_rect = pygame.Rect(content_rect.x + padding, chart_y, chart_width, chart_height)
        pygame.draw.rect(self.screen, (40, 40, 40), chart_bg_rect)

        # Draw grid lines
        for i in range(1, 4):  # Draw 3 horizontal grid lines
            y_pos = chart_y + (chart_height * i) // 4
            pygame.draw.line(self.screen, (60, 60, 60),
                           (chart_bg_rect.left, y_pos),
                           (chart_bg_rect.right, y_pos), 1)

        # Draw bars if we have data
        if prize_pool > 0 or revenue > 0:
            # Calculate max value for scaling
            max_value = max(prize_pool, revenue, 1)  # Avoid division by zero

            # Draw revenue bar
            bar_width = chart_width // 3
            revenue_height = (revenue / max_value) * chart_height
            revenue_rect = pygame.Rect(
                content_rect.x + padding + bar_width // 2,
                chart_y + chart_height - revenue_height,
                bar_width - 10,
                revenue_height
            )
            self.draw_gradient_rect(revenue_rect, (0, 180, 100), (0, 120, 60))

            # Draw prize pool bar
            prize_height = (prize_pool / max_value) * chart_height
            prize_rect = pygame.Rect(
                content_rect.x + padding + bar_width * 2,
                chart_y + chart_height - prize_height,
                bar_width - 10,
                prize_height
            )
            self.draw_gradient_rect(prize_rect, (255, 180, 0), (200, 120, 0))

            # Draw labels below bars
            rev_label = label_font.render("Revenue", True, (0, 180, 100))
            rev_label_rect = rev_label.get_rect(centerx=revenue_rect.centerx, top=chart_y + chart_height + 5)
            self.screen.blit(rev_label, rev_label_rect)

            prize_label = label_font.render("Prize Pool", True, GOLD)
            prize_label_rect = prize_label.get_rect(centerx=prize_rect.centerx, top=chart_y + chart_height + 5)
            self.screen.blit(prize_label, prize_label_rect)
        else:
            # No financial data available
            no_data = label_font.render("No financial data available", True, LIGHT_GRAY)
            no_data_rect = no_data.get_rect(center=(chart_bg_rect.centerx, chart_bg_rect.centery))
            self.screen.blit(no_data, no_data_rect)

    def draw_session_stats_section(self, x, y, width, height):
        """Draw the Session Statistics section"""
        # Draw the section with a purple title
        content_rect = self.draw_stats_section(x, y, width, height, "SESSION STATISTICS", (150, 80, 200))

        # Calculate layout for stats items
        padding = int(15 * min(self.scale_x, self.scale_y))
        item_height = int(30 * min(self.scale_x, self.scale_y))
        start_y = content_rect.y + padding

        # Prepare fonts
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
        value_font = pygame.font.SysFont("Arial", self.scaled_font_size(20), bold=True)
        small_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))

        # Get current time for session duration calculation
        current_time = time.time()

        # Get session start time from stats data
        session_start_time = self.stats_data.get("session_start_time", current_time)
        session_duration = current_time - session_start_time

        # Format session duration
        if session_duration > 0:
            hours = int(session_duration // 3600)
            minutes = int((session_duration % 3600) // 60)
            seconds = int(session_duration % 60)

            if hours > 0:
                duration_str = f"{hours}h {minutes}m {seconds}s"
            else:
                duration_str = f"{minutes}m {seconds}s"
        else:
            duration_str = "Just started"

        # Draw session duration
        self.draw_stat_item(content_rect.x + padding, start_y,
                          "Session Duration:", duration_str,
                          label_font, value_font, (180, 120, 220))

        # Draw active players in session
        active_players = len(self.players) if hasattr(self, 'players') and self.players else 0
        self.draw_stat_item(content_rect.x + padding, start_y + item_height,
                          "Players in Session:", str(active_players),
                          label_font, value_font, (180, 120, 220))

        # Check if payment system is available
        if hasattr(self, 'recharge_ui') and hasattr(self, 'recharge_button'):
            # Get voucher manager
            voucher_manager = self.recharge_ui.voucher_manager

            # Draw credit usage info
            credit_usage_title = label_font.render("Credit Usage:", True, (180, 120, 220))
            self.screen.blit(credit_usage_title, (content_rect.x + padding, start_y + item_height * 2))

            # Load and display credit usage information
            usage_log_path = os.path.join('data', 'usage_log.json')
            has_usage_data = False

            # Try to load usage log
            try:
                if os.path.exists(usage_log_path):
                    with open(usage_log_path, 'r') as f:
                        usage_log = json.load(f)

                    # Draw total usage
                    total_usage = usage_log.get("total_usage", 0)
                    total_usage_text = small_font.render(f"Total Credits Used: {total_usage}", True, (220, 180, 255))
                    self.screen.blit(total_usage_text, (content_rect.x + padding + 10, start_y + item_height * 2.7))

                    # Draw current balance
                    balance_text = small_font.render(f"Current Balance: {voucher_manager.credits} credits", True, (255, 215, 0))
                    self.screen.blit(balance_text, (content_rect.x + padding + 10, start_y + item_height * 3.2))

                    # Draw recent usage entries title
                    recent_usage_title = label_font.render("Recent Credit Usage:", True, (180, 120, 220))
                    self.screen.blit(recent_usage_title, (content_rect.x + padding, start_y + item_height * 4))

                    # Create a log area for credit usage
                    log_y = start_y + item_height * 4.7
                    log_height = height - (log_y - content_rect.y) - padding
                    log_width = width - padding * 2
                    log_rect = pygame.Rect(content_rect.x + padding, log_y, log_width, log_height)

                    # Draw log background with gradient
                    if hasattr(self, 'draw_gradient_rect'):
                        self.draw_gradient_rect(log_rect, (40, 30, 60), (30, 20, 40), 10)
                    else:
                        pygame.draw.rect(self.screen, (30, 30, 40), log_rect, border_radius=10)

                    # Draw border
                    pygame.draw.rect(self.screen, (80, 60, 100), log_rect, 1, border_radius=10)

                    # Get recent usage entries
                    usage_entries = usage_log.get("usage", [])

                    # If no usage entries are available, show a message
                    if not usage_entries:
                        no_usage = small_font.render("No credit usage recorded", True, LIGHT_GRAY)
                        no_usage_rect = no_usage.get_rect(center=(log_rect.centerx, log_rect.centery))
                        self.screen.blit(no_usage, no_usage_rect)
                    else:
                        # Draw usage entries (most recent first)
                        log_entry_height = int(25 * min(self.scale_x, self.scale_y))
                        log_entry_padding = int(5 * min(self.scale_x, self.scale_y))
                        log_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))

                        # Sort entries by timestamp (newest first)
                        sorted_entries = sorted(usage_entries, key=lambda x: x.get("timestamp", 0), reverse=True)

                        for i, entry in enumerate(sorted_entries[:5]):  # Show up to 5 entries
                            # Skip if we've run out of space
                            if i * log_entry_height >= log_height:
                                break

                            # Format timestamp
                            timestamp = entry.get("timestamp", 0)
                            time_str = time.strftime("%Y-%m-%d %H:%M", time.localtime(timestamp))

                            # Draw time
                            time_text = log_font.render(time_str, True, (200, 180, 220))
                            self.screen.blit(time_text, (log_rect.x + log_entry_padding,
                                                      log_rect.y + i * log_entry_height + log_entry_padding))

                            # Draw usage details
                            credits_used = entry.get("credits_used", 0)
                            share_percentage = entry.get("share_percentage", 0)
                            game_id = entry.get("game_id", "unknown")

                            # Create usage description
                            usage_desc = f"{credits_used} credits (Share: {share_percentage}%)"
                            usage_text = log_font.render(usage_desc, True, (255, 215, 0))
                            self.screen.blit(usage_text, (log_rect.x + int(150 * min(self.scale_x, self.scale_y)),
                                                       log_rect.y + i * log_entry_height + log_entry_padding))

                            # Draw game ID (shortened)
                            short_game_id = game_id[-8:] if len(game_id) > 8 else game_id
                            game_text = log_font.render(f"Game: {short_game_id}", True, LIGHT_GRAY)
                            self.screen.blit(game_text, (log_rect.x + log_entry_padding,
                                                      log_rect.y + i * log_entry_height + log_entry_padding + 15))

                    has_usage_data = True
            except Exception as e:
                print(f"Error loading usage log: {e}")
                error_text = small_font.render("Error loading credit usage data", True, RED)
                self.screen.blit(error_text, (content_rect.x + padding + 10, start_y + item_height * 3))
                has_usage_data = False

            # If no usage data was loaded, show regular activity log
            if not has_usage_data:
                # Draw regular activity log as fallback
                self._draw_activity_log(content_rect, padding, start_y, item_height, height, width, label_font)
        else:
            # Draw current prize pool
            prize_pool = self.stats_data["total_prize_pool"]
            prize_pool_str = f"{prize_pool:,} ETB" if prize_pool > 0 else "0 ETB"
            self.draw_stat_item(content_rect.x + padding, start_y + item_height * 2,
                              "Current Prize Pool:", prize_pool_str,
                              label_font, value_font, (180, 120, 220))

            # Draw regular activity log
            activity_title = label_font.render("Recent Activity:", True, (180, 120, 220))
            self.screen.blit(activity_title, (content_rect.x + padding, start_y + item_height * 3))

            # Create a log area
            log_y = start_y + item_height * 4
            log_height = height - (log_y - content_rect.y) - padding
            log_width = width - padding * 2
            log_rect = pygame.Rect(content_rect.x + padding, log_y, log_width, log_height)

            # Draw log background
            pygame.draw.rect(self.screen, (30, 30, 40), log_rect, border_radius=10)
            pygame.draw.rect(self.screen, (60, 60, 80), log_rect, 1, border_radius=10)  # Border

            # Get recent activity from stats data
            log_entries = self.stats_data.get("recent_activity", [])

            # If no activity is available, show a message
            if not log_entries:
                no_activity = label_font.render("No recent activity", True, LIGHT_GRAY)
                no_activity_rect = no_activity.get_rect(center=(log_rect.centerx, log_rect.centery))
                self.screen.blit(no_activity, no_activity_rect)
                return

            # Draw log entries
            log_entry_height = int(25 * min(self.scale_x, self.scale_y))
            log_entry_padding = int(5 * min(self.scale_x, self.scale_y))
            log_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))

            for i, entry in enumerate(log_entries):
                # Skip if we've run out of space
                if i * log_entry_height >= log_height:
                    break

                # Draw time
                time_text = log_font.render(entry["time"], True, (180, 180, 200))
                self.screen.blit(time_text, (log_rect.x + log_entry_padding,
                                          log_rect.y + i * log_entry_height + log_entry_padding))

                # Draw event description
                event_text = log_font.render(entry["event"], True, LIGHT_GRAY)
                self.screen.blit(event_text, (log_rect.x + int(60 * min(self.scale_x, self.scale_y)),
                                            log_rect.y + i * log_entry_height + log_entry_padding))

    def draw_stat_item(self, x, y, label, value, label_font, value_font, label_color):
        """Draw a statistic item with label and value"""
        # Draw label
        label_surf = label_font.render(label, True, label_color)
        self.screen.blit(label_surf, (x, y))

        # Draw value (right-aligned)
        value_surf = value_font.render(value, True, WHITE)
        value_x = x + int(200 * min(self.scale_x, self.scale_y)) - value_surf.get_width()
        self.screen.blit(value_surf, (value_x, y))

    def _draw_activity_log(self, content_rect, padding, start_y, item_height, height, width, label_font):
        """Draw the activity log section - extracted to reduce code duplication"""
        # Use cached fonts
        log_font = self.get_font("Arial", self.scaled_font_size(14))

        # Draw activity title
        activity_title = label_font.render("Recent Activity:", True, (180, 120, 220))
        self.screen.blit(activity_title, (content_rect.x + padding, start_y + item_height * 3))

        # Create a log area
        log_y = start_y + item_height * 4
        log_height = height - (log_y - content_rect.y) - padding
        log_width = width - padding * 2
        log_rect = pygame.Rect(content_rect.x + padding, log_y, log_width, log_height)

        # Draw log background - use a single surface for better performance
        log_bg = pygame.Surface((log_width, log_height), pygame.SRCALPHA)
        pygame.draw.rect(log_bg, (30, 30, 40), pygame.Rect(0, 0, log_width, log_height), border_radius=10)
        pygame.draw.rect(log_bg, (60, 60, 80), pygame.Rect(0, 0, log_width, log_height), 1, border_radius=10)
        self.screen.blit(log_bg, (log_rect.x, log_rect.y))

        # Get recent activity from stats data
        log_entries = self.stats_data.get("recent_activity", [])

        # If no activity is available, show a message
        if not log_entries:
            no_activity = label_font.render("No recent activity", True, LIGHT_GRAY)
            no_activity_rect = no_activity.get_rect(center=(log_rect.centerx, log_rect.centery))
            self.screen.blit(no_activity, no_activity_rect)
            return

        # Draw log entries
        log_entry_height = int(25 * min(self.scale_x, self.scale_y))
        log_entry_padding = int(5 * min(self.scale_x, self.scale_y))
        time_x = log_rect.x + log_entry_padding
        event_x = log_rect.x + int(60 * min(self.scale_x, self.scale_y))

        # Pre-calculate common values
        base_y = log_rect.y + log_entry_padding

        # Limit the number of entries to display based on available space
        max_entries = min(len(log_entries), int(log_height / log_entry_height))

        for i in range(max_entries):
            entry = log_entries[i]
            entry_y = base_y + i * log_entry_height

            # Draw time
            time_text = log_font.render(entry["time"], True, (180, 180, 200))
            self.screen.blit(time_text, (time_x, entry_y))

            # Draw event description
            event_text = log_font.render(entry["event"], True, LIGHT_GRAY)
            self.screen.blit(event_text, (event_x, entry_y))

    def scaled_font_size(self, base_size):
        """Calculate a scaled font size based on screen dimensions"""
        return int(base_size * min(self.scale_x, self.scale_y))

    def reset_loading_state_if_needed(self):
        """ANTI-BLINK: Only reset loading state when actually needed (e.g., new data load)."""
        with self._loading_state_lock:
            # Only reset if we're starting a completely new load operation
            if hasattr(self, '_force_reload_requested') and self._force_reload_requested:
                self.game_history_loading_complete = False
                self._stable_loading_complete = False
                self._force_reload_requested = False
                print("ANTI-BLINK: Loading state reset for new data load")
    
    def get_font(self, font_name, size, bold=False, italic=False):
        """Get a font from cache or create a new one if not cached"""
        # Create a unique key for this font configuration
        key = (font_name, size, bold, italic)

        # Return from cache if available
        if key in self._font_cache:
            return self._font_cache[key]

        # Create new font
        font = pygame.font.SysFont(font_name, size, bold, italic)

        # Cache the font
        self._font_cache[key] = font

        # Limit cache size to prevent memory issues
        if len(self._font_cache) > 30:  # Keep only 30 most recent fonts
            self._font_cache.pop(next(iter(self._font_cache)))

        return font
